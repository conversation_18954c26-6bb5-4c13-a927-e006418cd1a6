#!/usr/bin/env python3
"""
Comprehensive Social Media Competitor Analysis Tool

This script integrates Instagram post collection and comment scraping to provide
a complete competitor analysis across social media platforms.

Features:
- Collect competitor Instagram posts
- Scrape comments from those posts
- Analyze engagement patterns
- Generate comprehensive reports
- Support for multiple competitors
"""

import os
import json
from typing import Dict, List, Any
from datetime import datetime
from post_collector import Instagram, LinkedInCollector, Tiktok


class CompetitorSocialAnalyzer:
    """
    Comprehensive social media competitor analysis tool
    """
    
    def __init__(self):
        """Initialize the analyzer with social media collectors"""
        self.instagram = Instagram()
        self.linkedin = LinkedInCollector()
        self.tiktok = Tiktok()
        
    def analyze_instagram_competitor(
        self, 
        profile_url: str, 
        post_count: int = 10, 
        comment_limit: int = 15
    ) -> Dict[str, Any]:
        """
        Perform comprehensive Instagram competitor analysis
        
        Args:
            profile_url: Instagram profile URL or username
            post_count: Number of recent posts to analyze
            comment_limit: Number of comments to collect per post
            
        Returns:
            Complete analysis results
        """
        print(f"\n🔍 Analyzing Instagram competitor: {profile_url}")
        
        # Use the enhanced Instagram functionality
        result = self.instagram.run_with_comments(
            url=profile_url,
            post_count=post_count,
            comment_limit=comment_limit
        )
        
        if result["status"] == "success":
            # Add additional analysis
            result["engagement_analysis"] = self._analyze_engagement(result)
            result["content_insights"] = self._analyze_content(result)
            
        return result
    
    def analyze_multiple_competitors(
        self, 
        competitors: List[Dict[str, str]], 
        post_count: int = 5, 
        comment_limit: int = 10
    ) -> Dict[str, Any]:
        """
        Analyze multiple competitors across platforms
        
        Args:
            competitors: List of competitor info with social media URLs
            post_count: Posts to collect per competitor
            comment_limit: Comments to collect per post
            
        Returns:
            Comprehensive multi-competitor analysis
        """
        print(f"\n📊 Starting multi-competitor analysis for {len(competitors)} competitors")
        
        results = {
            "analysis_timestamp": datetime.now().isoformat(),
            "competitors": {},
            "comparative_analysis": {},
            "summary": {
                "total_competitors": len(competitors),
                "successful_analyses": 0,
                "total_posts_collected": 0,
                "total_comments_collected": 0
            }
        }
        
        for competitor in competitors:
            competitor_name = competitor.get("name", "Unknown")
            print(f"\n--- Analyzing {competitor_name} ---")
            
            competitor_results = {
                "name": competitor_name,
                "instagram": {},
                "linkedin": {},
                "tiktok": {},
                "overall_summary": {}
            }
            
            # Instagram Analysis
            if "instagram" in competitor:
                instagram_result = self.analyze_instagram_competitor(
                    competitor["instagram"], 
                    post_count, 
                    comment_limit
                )
                competitor_results["instagram"] = instagram_result
                
                if instagram_result["status"] == "success":
                    summary = instagram_result["summary"]
                    results["summary"]["total_posts_collected"] += summary["total_posts"]
                    results["summary"]["total_comments_collected"] += summary["total_comments"]
            
            # LinkedIn Analysis (posts only)
            if "linkedin" in competitor:
                print(f"📘 Collecting LinkedIn posts for {competitor_name}")
                linkedin_posts = self.linkedin.run(competitor["linkedin"])
                competitor_results["linkedin"] = {
                    "status": "success" if isinstance(linkedin_posts, list) else "error",
                    "posts": linkedin_posts if isinstance(linkedin_posts, list) else [],
                    "post_count": len(linkedin_posts) if isinstance(linkedin_posts, list) else 0
                }
            
            # TikTok Analysis (posts only)
            if "tiktok" in competitor:
                print(f"🎵 Collecting TikTok posts for {competitor_name}")
                tiktok_posts = self.tiktok.run(competitor["tiktok"], count=post_count)
                competitor_results["tiktok"] = {
                    "status": "success" if isinstance(tiktok_posts, list) else "error",
                    "posts": tiktok_posts if isinstance(tiktok_posts, list) else [],
                    "post_count": len(tiktok_posts) if isinstance(tiktok_posts, list) else 0
                }
            
            # Calculate overall summary for this competitor
            competitor_results["overall_summary"] = self._calculate_competitor_summary(competitor_results)
            
            results["competitors"][competitor_name] = competitor_results
            
            if any(platform.get("status") == "success" for platform in [
                competitor_results["instagram"], 
                competitor_results["linkedin"], 
                competitor_results["tiktok"]
            ]):
                results["summary"]["successful_analyses"] += 1
        
        # Generate comparative analysis
        results["comparative_analysis"] = self._generate_comparative_analysis(results["competitors"])
        
        print(f"\n✅ Multi-competitor analysis complete!")
        print(f"   - Analyzed: {results['summary']['successful_analyses']}/{results['summary']['total_competitors']} competitors")
        print(f"   - Total posts: {results['summary']['total_posts_collected']}")
        print(f"   - Total comments: {results['summary']['total_comments_collected']}")
        
        return results
    
    def _analyze_engagement(self, instagram_result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze engagement patterns from Instagram data"""
        if instagram_result["status"] != "success":
            return {"error": "No data to analyze"}
        
        posts = instagram_result.get("posts", [])
        total_comments = sum(post.get("comment_count", 0) for post in posts)
        
        # Find most engaging posts
        engaging_posts = sorted(posts, key=lambda x: x.get("comment_count", 0), reverse=True)[:3]
        
        return {
            "total_engagement": total_comments,
            "average_comments_per_post": total_comments / len(posts) if posts else 0,
            "most_engaging_posts": [
                {
                    "url": post["url"],
                    "comment_count": post.get("comment_count", 0),
                    "position": post.get("position", 0)
                }
                for post in engaging_posts
            ]
        }
    
    def _analyze_content(self, instagram_result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content patterns from comments"""
        if instagram_result["status"] != "success":
            return {"error": "No data to analyze"}
        
        all_comments = []
        for post in instagram_result.get("posts", []):
            all_comments.extend(post.get("comments", []))
        
        # Basic content analysis
        total_comments = len(all_comments)
        unique_commenters = len(set(comment.get("username", "") for comment in all_comments))
        
        # Find most active commenters
        commenter_counts = {}
        for comment in all_comments:
            username = comment.get("username", "")
            commenter_counts[username] = commenter_counts.get(username, 0) + 1
        
        top_commenters = sorted(commenter_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_comments": total_comments,
            "unique_commenters": unique_commenters,
            "engagement_diversity": unique_commenters / total_comments if total_comments > 0 else 0,
            "top_commenters": [{"username": username, "comment_count": count} for username, count in top_commenters]
        }
    
    def _calculate_competitor_summary(self, competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall summary for a single competitor"""
        total_posts = 0
        total_comments = 0
        active_platforms = 0
        
        for platform in ["instagram", "linkedin", "tiktok"]:
            platform_data = competitor_data.get(platform, {})
            if platform_data.get("status") == "success":
                active_platforms += 1
                if platform == "instagram":
                    summary = platform_data.get("summary", {})
                    total_posts += summary.get("total_posts", 0)
                    total_comments += summary.get("total_comments", 0)
                else:
                    total_posts += platform_data.get("post_count", 0)
        
        return {
            "active_platforms": active_platforms,
            "total_posts": total_posts,
            "total_comments": total_comments,
            "platforms_analyzed": ["instagram", "linkedin", "tiktok"]
        }
    
    def _generate_comparative_analysis(self, competitors_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comparative analysis across all competitors"""
        platform_leaders = {
            "instagram_most_posts": {"name": "", "count": 0},
            "instagram_most_comments": {"name": "", "count": 0},
            "linkedin_most_posts": {"name": "", "count": 0},
            "tiktok_most_posts": {"name": "", "count": 0}
        }
        
        for name, data in competitors_data.items():
            # Instagram comparison
            instagram_data = data.get("instagram", {})
            if instagram_data.get("status") == "success":
                summary = instagram_data.get("summary", {})
                posts = summary.get("total_posts", 0)
                comments = summary.get("total_comments", 0)
                
                if posts > platform_leaders["instagram_most_posts"]["count"]:
                    platform_leaders["instagram_most_posts"] = {"name": name, "count": posts}
                
                if comments > platform_leaders["instagram_most_comments"]["count"]:
                    platform_leaders["instagram_most_comments"] = {"name": name, "count": comments}
            
            # LinkedIn comparison
            linkedin_data = data.get("linkedin", {})
            if linkedin_data.get("status") == "success":
                posts = linkedin_data.get("post_count", 0)
                if posts > platform_leaders["linkedin_most_posts"]["count"]:
                    platform_leaders["linkedin_most_posts"] = {"name": name, "count": posts}
            
            # TikTok comparison
            tiktok_data = data.get("tiktok", {})
            if tiktok_data.get("status") == "success":
                posts = tiktok_data.get("post_count", 0)
                if posts > platform_leaders["tiktok_most_posts"]["count"]:
                    platform_leaders["tiktok_most_posts"] = {"name": name, "count": posts}
        
        return {
            "platform_leaders": platform_leaders,
            "analysis_summary": "Comparative analysis complete"
        }
    
    def save_analysis_report(self, analysis_results: Dict[str, Any], filename: str = None) -> str:
        """Save analysis results to a JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"competitor_analysis_{timestamp}.json"
        
        filepath = os.path.join(os.path.dirname(__file__), filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Analysis report saved to: {filepath}")
        return filepath


def main():
    """Example usage of the comprehensive competitor analysis tool"""
    analyzer = CompetitorSocialAnalyzer()
    
    # Example competitors with their social media profiles
    competitors = [
        {
            "name": "John Deere",
            "instagram": "https://www.instagram.com/johndeere/",
            "linkedin": "https://www.linkedin.com/company/john-deere",
            "tiktok": "https://www.tiktok.com/@johndeere"
        },
        {
            "name": "Caterpillar",
            "instagram": "https://www.instagram.com/caterpillarinc/",
            "linkedin": "https://www.linkedin.com/company/caterpillar-inc",
        }
    ]
    
    # Run comprehensive analysis
    results = analyzer.analyze_multiple_competitors(
        competitors=competitors,
        post_count=5,  # Collect 5 recent posts per platform
        comment_limit=10  # Collect up to 10 comments per Instagram post
    )
    
    # Save the results
    report_file = analyzer.save_analysis_report(results)
    
    # Display summary
    print(f"\n📋 ANALYSIS SUMMARY")
    print(f"=" * 50)
    summary = results["summary"]
    print(f"Competitors analyzed: {summary['successful_analyses']}/{summary['total_competitors']}")
    print(f"Total posts collected: {summary['total_posts_collected']}")
    print(f"Total comments collected: {summary['total_comments_collected']}")
    
    # Show platform leaders
    leaders = results["comparative_analysis"]["platform_leaders"]
    print(f"\n🏆 PLATFORM LEADERS")
    print(f"Instagram (most posts): {leaders['instagram_most_posts']['name']} ({leaders['instagram_most_posts']['count']} posts)")
    print(f"Instagram (most comments): {leaders['instagram_most_comments']['name']} ({leaders['instagram_most_comments']['count']} comments)")
    print(f"LinkedIn (most posts): {leaders['linkedin_most_posts']['name']} ({leaders['linkedin_most_posts']['count']} posts)")
    print(f"TikTok (most posts): {leaders['tiktok_most_posts']['name']} ({leaders['tiktok_most_posts']['count']} posts)")


if __name__ == "__main__":
    main()
