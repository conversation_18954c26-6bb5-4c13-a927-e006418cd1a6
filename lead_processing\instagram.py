from apify_client import ApifyClient
import json

def scrape_instagram_post(url: str, api_token: str, limit: int = 10, timeout: int = 120) -> dict:
    """
    Scrape Instagram post data using Apify actor

    Args:
        url: Instagram post URL to scrape
        api_token: Apify API token
        limit: Maximum number of results to return
        timeout: Maximum time to wait for the actor to complete (seconds)

    Returns:
        dict: Scraped data with specific comment fields
    """
    # Initialize client
    client = ApifyClient(api_token)

    # Configure input
    run_input = {
        "directUrls": [url],
        "resultsLimit": limit
    }

    try:
        print(f"Starting Instagram scrape for URL: {url}")
        print(f"Results limit: {limit}")

        # Run actor and get results
        run = client.actor("SbK00X0JYCPblD2wp").call(run_input=run_input, timeout_secs=timeout)

        print(f"Actor run completed. Dataset ID: {run['defaultDatasetId']}")

        # Collect results with specific fields
        results = []
        item_count = 0
        for item in client.dataset(run["defaultDatasetId"]).iterate_items():
            item_count += 1
            comment_data = {
                "text": item.get("text", ""),
                "username": item.get("ownerUsername", ""),
                "full_name": item.get("owner", {}).get("full_name", ""),
                # "likes_count": item.get("likesCount", 0),
                "profile_url": item.get("ownerProfilePicUrl", "")
            }
            results.append(comment_data)

        print(f"Processed {item_count} items from dataset")

        return {
            "status": "success",
            "data": results,
            "total_items": len(results)
        }

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

# Example usage
if __name__ == "__main__":
    API_TOKEN = "**********************************************"
    POST_URL = "https://www.instagram.com/p/DJtyFcRM-Xn/"

    result = scrape_instagram_post(POST_URL, API_TOKEN)

    # Print results in a more readable format
    if result["status"] == "success":
        print("\nComments:")
        for comment in result["data"]:
            print("\nComment:")
            print(f"Text: {comment['text']}")
            print(f"Username: {comment['username']}")
            print(f"Full Name: {comment['full_name']}")
            # print(f"Likes: {comment['likes_count']}")
            print(f"Profile URL: {comment['profile_url']}")
    else:
        print(f"Error: {result['message']}")
