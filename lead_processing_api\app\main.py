from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .database import engine
from .models import Base
from .routes import jobs, website, competitors, social_posts, auth

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Lead Processing API",
    description="API for processing business leads and competitor analysis",
    version="1.0.0",
    openapi_tags=[
        {
            "name": "authentication",
            "description": "User authentication and authorization endpoints"
        },
        {
            "name": "website",
            "description": "Website analysis and information extraction"
        },
        {
            "name": "competitors",
            "description": "Competitor analysis and business intelligence"
        },
        {
            "name": "social-posts",
            "description": "Social media post collection and analysis"
        },
        {
            "name": "jobs",
            "description": "Background job management and processing"
        },
        {
            "name": "system",
            "description": "System health and information endpoints"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(website.router)
app.include_router(competitors.router)
app.include_router(social_posts.router)
app.include_router(jobs.router)

@app.get("/", tags=["system"])
def read_root():
    """Root endpoint with API information."""
    return {
        "message": "Lead Processing API",
        "version": "1.0.0",
        "docs": "/docs",
        "endpoints": {
            "authentication": {
                "register": "POST /auth/register",
                "login": "POST /auth/token"
            },
            "website_analysis": {
                "analyze_website": "POST /website/analyze",
                "website_health": "GET /website/health"
            },
            "competitor_analysis": {
                "analyze_competitors": "POST /competitors/analyze",
                "extract_business_info": "POST /competitors/extract-business-info",
                "competitors_health": "GET /competitors/health"
            },
            "social_media": {
                "competitor_analysis": "POST /social-posts/competitor/analyze",
                "collect_competitor_posts": "POST /social-posts/competitor",
                "social_posts_health": "GET /social-posts/health"
            },
            "jobs": {
                "create_job": "POST /jobs/ (🔒 Auth Required)",
                "get_job": "GET /jobs/{job_id} (🔒 Auth Required)",
                "get_job_status": "GET /jobs/{job_id}/status (🔒 Auth Required)",
                "get_job_results": "GET /jobs/{job_id}/results (🔒 Auth Required)",
                "list_jobs": "GET /jobs/ (🔒 Auth Required)",
                "delete_job": "DELETE /jobs/{job_id} (🔒 Auth Required)"
            }
        }
    }

@app.get("/health", tags=["system"])
def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}
