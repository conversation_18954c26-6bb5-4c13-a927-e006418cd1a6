from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional
from sqlalchemy.orm import Session

from ..database import get_db
from ..services.competitor_service import CompetitorService
from ..services.social_media_collectors import <PERSON>sta<PERSON><PERSON>ollector, LinkedInCollector, TiktokCollector
from ..schemas import (
    UnifiedCompetitorRequest,
    UnifiedCompetitorResponse
)

router = APIRouter(
    prefix="/social-posts",
    tags=["social-posts"],
)

# Pydantic models for remaining endpoints

class CompetitorPostsRequest(BaseModel):
    """Request model for collecting posts from a competitor."""
    name: str
    linkedin: Optional[str] = "N/A"
    instagram: Optional[str] = "N/A"
    facebook: Optional[str] = "N/A"
    tiktok: Optional[str] = "N/A"

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "linkedin": "https://www.linkedin.com/company/example-company",
                "instagram": "https://www.instagram.com/examplecompany/",
                "facebook": "https://www.facebook.com/ExampleCompany",
                "tiktok": "N/A"
            }
        }

# SocialPostsResponse removed - individual platform endpoints removed

class CompetitorPostsResponse(BaseModel):
    """Response model for competitor posts across all platforms."""
    name: str
    posts: Dict[str, List[str]]
    total_posts_collected: int

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "posts": {
                    "linkedin": ["https://linkedin.com/posts/post1"],
                    "instagram": ["https://instagram.com/p/post1"],
                    "facebook": [],
                    "tiktok": []
                },
                "total_posts_collected": 2
            }
        }

@router.post("/competitor", response_model=CompetitorPostsResponse)
def collect_competitor_posts(request: CompetitorPostsRequest):
    """
    Collect posts from all social media platforms for a competitor.

    This endpoint uses the main lead processing logic to collect posts
    from LinkedIn, Instagram, Facebook, and TikTok for a single competitor.

    **Parameters:**
    - **name**: Competitor name
    - **linkedin**: LinkedIn company URL (optional)
    - **instagram**: Instagram profile URL (optional)
    - **facebook**: Facebook page URL (optional)
    - **tiktok**: TikTok profile URL (optional)

    **Returns:**
    - Posts collected from all available platforms
    """
    try:
        # Initialize collectors
        instagram_collector = InstagramCollector()
        linkedin_collector = LinkedInCollector()
        tiktok_collector = TiktokCollector()

        # Collect posts from each platform
        cleaned_posts = {}
        total_posts = 0

        # LinkedIn posts
        if request.linkedin:
            try:
                linkedin_posts = linkedin_collector.run(request.linkedin)
                cleaned_posts["linkedin"] = linkedin_posts if isinstance(linkedin_posts, list) else []
                total_posts += len(cleaned_posts["linkedin"])
            except Exception as e:
                print(f"LinkedIn collection failed: {e}")
                cleaned_posts["linkedin"] = []

        # Instagram posts
        if request.instagram:
            try:
                instagram_posts = instagram_collector.run(request.instagram, count=10)
                cleaned_posts["instagram"] = instagram_posts if isinstance(instagram_posts, list) else []
                total_posts += len(cleaned_posts["instagram"])
            except Exception as e:
                print(f"Instagram collection failed: {e}")
                cleaned_posts["instagram"] = []

        # TikTok posts
        if request.tiktok:
            try:
                tiktok_posts = tiktok_collector.run(request.tiktok, count=10)
                cleaned_posts["tiktok"] = tiktok_posts if isinstance(tiktok_posts, list) else []
                total_posts += len(cleaned_posts["tiktok"])
            except Exception as e:
                print(f"TikTok collection failed: {e}")
                cleaned_posts["tiktok"] = []

        # Facebook not implemented yet
        if request.facebook:
            cleaned_posts["facebook"] = []

        response = CompetitorPostsResponse(
            name=request.name,
            posts=cleaned_posts,
            total_posts_collected=total_posts
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Competitor post collection failed: {str(e)}"
        )

@router.get("/health")
def social_posts_health_check():
    """Health check for social media post collection functionality."""
    try:
        # Test if collectors can be initialized
        instagram_collector = InstagramCollector()
        linkedin_collector = LinkedInCollector()
        tiktok_collector = TiktokCollector()

        status = {
            "instagram_collector": True,
            "linkedin_collector": True,
            "tiktok_collector": True,
            "competitor_service": True
        }

        return {"status": "available", "message": "Social media post collection ready", "modules": status}
    except Exception as e:
        return {"status": "error", "message": f"Service initialization failed: {str(e)}", "modules": {}}

# Unified Competitor Analysis Endpoint

@router.post("/competitor/analyze", response_model=UnifiedCompetitorResponse)
def analyze_competitor_comprehensive(
    request: UnifiedCompetitorRequest,
    db: Session = Depends(get_db)
):
    """
    🚀 **UNIFIED COMPETITOR ANALYSIS**

    **One endpoint to rule them all!**

    Input a competitor website URL and get:
    ✅ **Social media links** extracted from the website
    ✅ **Posts collected** from ALL platforms (LinkedIn, Instagram, TikTok, Facebook)
    ✅ **Instagram comments** scraped from all Instagram posts
    ✅ **Comprehensive analytics** and engagement insights

    **What this endpoint does:**
    1. 🔍 Extracts social media links from competitor website
    2. 📱 Collects posts from LinkedIn, Instagram, TikTok
    3. 💬 Scrapes comments from Instagram posts (with engagement data)
    4. 📊 Provides comprehensive analytics and insights
    5. 💾 Optionally saves everything to database

    **Parameters:**
    - **website_url**: Competitor website URL (e.g., "https://johndeere.com")
    - **post_count**: Number of posts to collect per platform (default: 10)
    - **comment_limit**: Number of comments to scrape per Instagram post (default: 15)
    - **save_to_db**: Whether to save results to database (default: false)

    **Returns:**
    - **social_media_links**: All social media URLs found on the website
    - **posts**: Posts from all platforms with URLs and metadata
    - **instagram_comments**: Comments from Instagram posts with user data
    - **summary**: Comprehensive analytics and platform breakdown
    - **instagram_analysis**: Instagram-specific engagement metrics

    ```
    """
    competitor_service = CompetitorService()

    try:
        result = competitor_service.analyze_competitor_comprehensive(
            website_url=request.website_url,
            post_count=request.post_count,
            comment_limit=request.comment_limit,
            save_to_db=request.save_to_db,
            db=db if request.save_to_db else None
        )

        return UnifiedCompetitorResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unified competitor analysis failed: {str(e)}"
        )
