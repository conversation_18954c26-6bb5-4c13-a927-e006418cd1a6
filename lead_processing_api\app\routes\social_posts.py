from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
import sys
import os

from ..database import get_db
from ..services.unified_competitor_service import UnifiedCompetitorService
from ..schemas import (
    UnifiedCompetitorRequest,
    UnifiedCompetitorResponse
)

# Add the lead_processing directory to the path
lead_processing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))
if lead_processing_path not in sys.path:
    sys.path.insert(0, lead_processing_path)

# Import the modules
post_collector = None
lead_main = None

try:
    import post_collector
    print("✅ Social posts route: Successfully imported post_collector")
except ImportError as e:
    print(f"⚠️  Social posts route: Could not import post_collector: {e}")

try:
    import lead_processing.main as lead_main
    print("✅ Social posts route: Successfully imported lead_processing main")
except ImportError as e:
    print(f"⚠️  Social posts route: Could not import lead_processing main: {e}")

router = APIRouter(
    prefix="/social-posts",
    tags=["social-posts"],
)

# Pydantic models for remaining endpoints

class CompetitorPostsRequest(BaseModel):
    """Request model for collecting posts from a competitor."""
    name: str
    linkedin: Optional[str] = "N/A"
    instagram: Optional[str] = "N/A"
    facebook: Optional[str] = "N/A"
    tiktok: Optional[str] = "N/A"

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "linkedin": "https://www.linkedin.com/company/example-company",
                "instagram": "https://www.instagram.com/examplecompany/",
                "facebook": "https://www.facebook.com/ExampleCompany",
                "tiktok": "N/A"
            }
        }

# SocialPostsResponse removed - individual platform endpoints removed

class CompetitorPostsResponse(BaseModel):
    """Response model for competitor posts across all platforms."""
    name: str
    posts: Dict[str, List[str]]
    total_posts_collected: int

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "posts": {
                    "linkedin": ["https://linkedin.com/posts/post1"],
                    "instagram": ["https://instagram.com/p/post1"],
                    "facebook": [],
                    "tiktok": []
                },
                "total_posts_collected": 2
            }
        }

@router.post("/competitor", response_model=CompetitorPostsResponse)
def collect_competitor_posts(request: CompetitorPostsRequest):
    """
    Collect posts from all social media platforms for a competitor.

    This endpoint uses the main lead processing logic to collect posts
    from LinkedIn, Instagram, Facebook, and TikTok for a single competitor.

    **Parameters:**
    - **name**: Competitor name
    - **linkedin**: LinkedIn company URL (optional)
    - **instagram**: Instagram profile URL (optional)
    - **facebook**: Facebook page URL (optional)
    - **tiktok**: TikTok profile URL (optional)

    **Returns:**
    - Posts collected from all available platforms
    """
    if lead_main is None:
        raise HTTPException(
            status_code=500,
            detail="Lead processing main module not available"
        )

    try:
        # Create competitor dictionary in the format expected by the main module
        competitor = {
            'name': request.name,
            'linkedin': request.linkedin or 'N/A',
            'instagram': request.instagram or 'N/A',
            'facebook': request.facebook or 'N/A',
            'tiktok': request.tiktok or 'N/A'
        }

        # Use the existing collect_social_media_posts function
        result = lead_main.collect_social_media_posts(competitor)

        # Extract posts from the result
        posts = result.get('posts', {})

        # Clean up posts - convert error strings to empty lists
        cleaned_posts = {}
        for platform, platform_posts in posts.items():
            if isinstance(platform_posts, str) and "No posts found" in platform_posts:
                cleaned_posts[platform] = []
            elif isinstance(platform_posts, list):
                cleaned_posts[platform] = platform_posts
            else:
                cleaned_posts[platform] = []

        # Count total posts
        total_posts = 0
        for platform_posts in cleaned_posts.values():
            if isinstance(platform_posts, list):
                total_posts += len(platform_posts)

        response = CompetitorPostsResponse(
            name=request.name,
            posts=cleaned_posts,
            total_posts_collected=total_posts
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Competitor post collection failed: {str(e)}"
        )

@router.get("/health")
def social_posts_health_check():
    """Health check for social media post collection functionality."""
    status = {
        "post_collector": post_collector is not None,
        "lead_main": lead_main is not None
    }

    if all(status.values()):
        return {"status": "available", "message": "Social media post collection ready", "modules": status}
    else:
        return {"status": "partial", "message": "Some modules not loaded", "modules": status}

# Unified Competitor Analysis Endpoint

@router.post("/competitor/analyze", response_model=UnifiedCompetitorResponse)
def analyze_competitor_comprehensive(
    request: UnifiedCompetitorRequest,
    db: Session = Depends(get_db)
):
    """
    🚀 **UNIFIED COMPETITOR ANALYSIS**

    **One endpoint to rule them all!**

    Input a competitor website URL and get:
    ✅ **Social media links** extracted from the website
    ✅ **Posts collected** from ALL platforms (LinkedIn, Instagram, TikTok, Facebook)
    ✅ **Instagram comments** scraped from all Instagram posts
    ✅ **Comprehensive analytics** and engagement insights

    **What this endpoint does:**
    1. 🔍 Extracts social media links from competitor website
    2. 📱 Collects posts from LinkedIn, Instagram, TikTok
    3. 💬 Scrapes comments from Instagram posts (with engagement data)
    4. 📊 Provides comprehensive analytics and insights
    5. 💾 Optionally saves everything to database

    **Parameters:**
    - **website_url**: Competitor website URL (e.g., "https://johndeere.com")
    - **post_count**: Number of posts to collect per platform (default: 10)
    - **comment_limit**: Number of comments to scrape per Instagram post (default: 15)
    - **save_to_db**: Whether to save results to database (default: false)

    **Returns:**
    - **social_media_links**: All social media URLs found on the website
    - **posts**: Posts from all platforms with URLs and metadata
    - **instagram_comments**: Comments from Instagram posts with user data
    - **summary**: Comprehensive analytics and platform breakdown
    - **instagram_analysis**: Instagram-specific engagement metrics

    ```
    """
    unified_service = UnifiedCompetitorService()

    try:
        result = unified_service.analyze_competitor_comprehensive(
            website_url=request.website_url,
            post_count=request.post_count,
            comment_limit=request.comment_limit,
            save_to_db=request.save_to_db,
            db=db if request.save_to_db else None
        )

        return UnifiedCompetitorResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unified competitor analysis failed: {str(e)}"
        )
