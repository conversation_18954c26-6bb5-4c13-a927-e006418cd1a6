# 📧 Email Service Setup Guide

## Overview

The Lead Processing API includes a comprehensive email service that sends:
- ✅ **Welcome emails** for new user registrations
- ✅ **Job completion notifications** with detailed results
- ✅ **Error notifications** when jobs fail
- ✅ **Custom HTML templates** with professional styling

## 🔧 Setup Options

### Option 1: Gmail with App Password (Recommended)

#### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **Security** in the left sidebar
3. Under "Signing in to Google", click **2-Step Verification**
4. Follow the setup process to enable 2FA

#### Step 2: Generate App Password
1. In Google Account Settings → **Security**
2. Under "Signing in to Google", click **App passwords**
3. Select **Mail** from the dropdown
4. Click **Generate**
5. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

#### Step 3: Update .env File
```bash
# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_16_character_app_password
FROM_EMAIL=<EMAIL>
```

### Option 2: Other Email Providers

#### Outlook/Hotmail
```bash
SMTP_SERVER=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_password
FROM_EMAIL=<EMAIL>
```

#### Yahoo Mail
```bash
SMTP_SERVER=smtp.mail.yahoo.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
```

#### Custom SMTP Server
```bash
SMTP_SERVER=your.smtp.server.com
SMTP_PORT=587
SMTP_USERNAME=your_username
SMTP_PASSWORD=your_password
FROM_EMAIL=<EMAIL>
```

## 🧪 Testing the Email Service

### Run the Test Script
```bash
cd lead_processing_api
python test_email_service.py
```

### Expected Output
```
🧪 Email Service Test Suite
==================================================
🔧 Testing Email Service Configuration...
📧 SMTP Server: smtp.gmail.com
📧 SMTP Port: 587
📧 Username: <EMAIL>
📧 From Email: <EMAIL>
📧 Is Configured: True

✅ Email service is configured!

📧 Testing Welcome Email...
✅ Welcome email sent <NAME_EMAIL>

📧 Testing Job Completion Email...
✅ Job completion email sent <NAME_EMAIL>

📧 Testing Error Notification Email...
✅ Error notification email sent <NAME_EMAIL>

==================================================
📊 Test Results Summary:
  Welcome Email: ✅ PASSED
  Job Completion Email: ✅ PASSED
  Error Notification Email: ✅ PASSED

🎉 All email tests passed! Email service is working correctly.
📧 Check your inbox for the test emails.
```

## 📧 Email Templates

### Welcome Email
- **Subject**: "Welcome to Lead Processing API"
- **Content**: Professional welcome message with feature overview
- **Style**: Blue header with feature highlights

### Job Completion Email
- **Subject**: "Lead Processing Job #123 Completed"
- **Content**: Detailed results with competitor data and social media links
- **Style**: Green header with statistics and competitor breakdown

### Error Notification Email
- **Subject**: "Lead Processing Job #123 Failed"
- **Content**: Error details and next steps
- **Style**: Red header with error information

## 🔍 Troubleshooting

### Common Issues

#### 1. "Email service not configured"
**Solution**: Set `SMTP_USERNAME` and `SMTP_PASSWORD` in `.env`

#### 2. "Authentication failed"
**Solutions**:
- Use App Password instead of regular password (Gmail)
- Enable "Less secure app access" (not recommended)
- Check username/password are correct

#### 3. "Connection refused"
**Solutions**:
- Check SMTP server and port
- Verify firewall settings
- Try different port (465 for SSL)

#### 4. "SSL/TLS errors"
**Solutions**:
- Use port 587 with STARTTLS
- Use port 465 with SSL
- Check email provider documentation

### Debug Mode
Add this to your `.env` for detailed email debugging:
```bash
EMAIL_DEBUG=true
```

## 🚀 Usage in API

### Automatic Emails
The email service automatically sends emails when:
- New users register (welcome email)
- Jobs complete successfully (completion notification)
- Jobs fail (error notification)

### Manual Email Testing
```python
from app.services.email_service import EmailService

email_service = EmailService()

# Send welcome email
email_service.send_welcome_email(
    to_email="<EMAIL>",
    username="John Doe"
)

# Send job completion email
email_service.send_job_completion_email(
    to_email="<EMAIL>",
    job=job_instance,
    competitors=competitors_list
)
```

## 📞 Support

If you're still having issues:
1. Run the test script: `python test_email_service.py`
2. Check your email provider's SMTP documentation
3. Verify 2FA and app passwords are set up correctly
4. Check firewall and antivirus settings

The email service provides professional, HTML-formatted emails that enhance the user experience of your Lead Processing API! 🎉
