#!/usr/bin/env python3
"""
Test script to show Instagram posts and comments data structure
"""

import requests
import json
import sys

def test_instagram_data_structure():
    """Test and display the Instagram data structure."""
    
    # API endpoint
    base_url = "http://127.0.0.1:8000"
    endpoint = f"{base_url}/social-posts/competitor/analyze"
    
    # Test payload - using a company that likely has Instagram
    payload = {
        "website_url": "https://nike.com",  # Nike likely has Instagram
        "post_count": 5,
        "comment_limit": 10,
        "save_to_db": False
    }
    
    print("🧪 Testing Instagram Posts and Comments Data")
    print("=" * 60)
    print(f"📍 Testing with: {payload['website_url']}")
    
    try:
        print("\n📤 Sending request...")
        response = requests.post(endpoint, json=payload, timeout=300)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Request successful!")
            
            # Display social media links found
            social_links = data.get('social_media_links', {})
            print(f"\n📱 Social Media Links Found:")
            for platform, url in social_links.items():
                print(f"   {platform}: {url}")
            
            # Display posts data
            posts = data.get('posts', {})
            print(f"\n📊 Posts Data:")
            for platform, platform_data in posts.items():
                print(f"\n   {platform.upper()}:")
                print(f"     Platform URL: {platform_data.get('platform_url')}")
                print(f"     Posts Count: {platform_data.get('posts_count')}")
                print(f"     Status: {platform_data.get('status')}")
                
                post_urls = platform_data.get('posts', [])
                if post_urls:
                    print(f"     Post URLs:")
                    for i, url in enumerate(post_urls, 1):
                        print(f"       {i}. {url}")
                else:
                    print(f"     No posts found")
            
            # Display Instagram comments
            instagram_comments = data.get('instagram_comments', {})
            print(f"\n💬 Instagram Comments:")
            
            if instagram_comments:
                for post_url, comment_data in instagram_comments.items():
                    print(f"\n   📍 Post: {post_url}")
                    print(f"     Comment Count: {comment_data.get('comment_count')}")
                    print(f"     Status: {comment_data.get('status')}")
                    
                    comments = comment_data.get('comments', [])
                    if comments:
                        print(f"     Comments:")
                        for i, comment in enumerate(comments[:3], 1):  # Show first 3 comments
                            username = comment.get('username', 'Unknown')
                            text = comment.get('text', '')[:100]  # First 100 chars
                            likes = comment.get('likes_count', 0)
                            print(f"       {i}. @{username}: {text}... (👍 {likes})")
                        
                        if len(comments) > 3:
                            print(f"       ... and {len(comments) - 3} more comments")
                    else:
                        error = comment_data.get('error', 'No error message')
                        print(f"     No comments found. Error: {error}")
            else:
                print("   No Instagram comments data found")
            
            # Display Instagram analysis
            instagram_analysis = data.get('instagram_analysis', {})
            if instagram_analysis:
                print(f"\n📈 Instagram Analysis:")
                print(f"   Total Posts Analyzed: {instagram_analysis.get('total_posts_analyzed')}")
                print(f"   Total Comments Collected: {instagram_analysis.get('total_comments_collected')}")
                print(f"   Average Comments per Post: {instagram_analysis.get('average_comments_per_post', 0):.1f}")
            
            # Display summary
            summary = data.get('summary', {})
            print(f"\n📋 Summary:")
            print(f"   Platforms Analyzed: {summary.get('platforms_analyzed')}")
            print(f"   Platforms With Posts: {summary.get('platforms_with_posts')}")
            print(f"   Total Posts Collected: {summary.get('total_posts_collected')}")
            print(f"   Total Instagram Comments: {summary.get('total_instagram_comments')}")
            
            return True
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_data_access_examples():
    """Show examples of how to access the Instagram data."""
    print("\n" + "=" * 60)
    print("📚 How to Access Instagram Data in Your Code:")
    print("=" * 60)
    
    print("""
# Example: Extract Instagram post URLs
response_data = api_response.json()
posts = response_data.get('posts', {})
instagram_data = posts.get('instagram', {})
instagram_post_urls = instagram_data.get('posts', [])

print("Instagram Posts:")
for url in instagram_post_urls:
    print(f"  - {url}")

# Example: Extract Instagram comments
instagram_comments = response_data.get('instagram_comments', {})
for post_url, comment_data in instagram_comments.items():
    comments = comment_data.get('comments', [])
    print(f"\\nPost: {post_url}")
    print(f"Comments ({len(comments)}):")
    
    for comment in comments:
        username = comment.get('username')
        text = comment.get('text')
        likes = comment.get('likes_count', 0)
        print(f"  @{username}: {text} (👍 {likes})")

# Example: Get Instagram statistics
instagram_analysis = response_data.get('instagram_analysis', {})
total_posts = instagram_analysis.get('total_posts_analyzed', 0)
total_comments = instagram_analysis.get('total_comments_collected', 0)
avg_comments = instagram_analysis.get('average_comments_per_post', 0)

print(f"Instagram Stats: {total_posts} posts, {total_comments} comments, {avg_comments:.1f} avg")
""")

def main():
    """Run the test."""
    print("🚀 Instagram Posts and Comments Data Test")
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running properly")
            sys.exit(1)
    except:
        print("❌ Server is not running")
        print("💡 Start with: uvicorn app.main:app --reload")
        sys.exit(1)
    
    # Test the data structure
    success = test_instagram_data_structure()
    
    if success:
        show_data_access_examples()
        print("\n🎉 The API already returns Instagram posts and comments!")
        print("📱 Use the examples above to access the data in your application.")
    else:
        print("\n❌ Test failed. Check the error messages above.")

if __name__ == "__main__":
    main()
