#!/usr/bin/env python3
"""
Debug script to test social media link extraction
"""

import requests
from bs4 import BeautifulSoup
import re
import json

def test_social_link_extraction(url):
    """Test social media link extraction for a specific URL."""
    print(f"🔍 Testing social media link extraction for: {url}")
    print("=" * 60)
    
    # Enhanced headers
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }
    
    try:
        session = requests.Session()
        session.headers.update(headers)
        
        print("📤 Making request...")
        response = session.get(url, timeout=15, allow_redirects=True)
        
        print(f"✅ Status: {response.status_code}")
        print(f"📏 Content Length: {len(response.content)} bytes")
        print(f"🔗 Final URL: {response.url}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Method 1: Look for social media links in href attributes
        print("\n🔍 Method 1: Searching for social media links in href attributes...")
        social_patterns = {
            'instagram': [r'instagram\.com/[^/\s"\']+', r'instagr\.am/[^/\s"\']+'],
            'linkedin': [r'linkedin\.com/company/[^/\s"\']+', r'linkedin\.com/in/[^/\s"\']+'],
            'facebook': [r'facebook\.com/[^/\s"\']+', r'fb\.com/[^/\s"\']+'],
            'tiktok': [r'tiktok\.com/@[^/\s"\']+', r'tiktok\.com/[^/\s"\']+'],
            'twitter': [r'twitter\.com/[^/\s"\']+', r'x\.com/[^/\s"\']+'],
            'youtube': [r'youtube\.com/[^/\s"\']+', r'youtu\.be/[^/\s"\']+']
        }
        
        found_links = {}
        html_content = str(soup)
        
        for platform, patterns in social_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    # Clean up the match
                    clean_match = matches[0].split('"')[0].split("'")[0].split()[0]
                    if not clean_match.startswith('http'):
                        clean_match = 'https://' + clean_match
                    found_links[platform] = clean_match
                    print(f"  ✅ {platform}: {clean_match}")
                    break
        
        if not found_links:
            print("  ❌ No social media links found with regex patterns")
        
        # Method 2: Look for social media links in specific elements
        print("\n🔍 Method 2: Searching in footer and social sections...")
        social_sections = soup.find_all(['footer', 'div'], class_=re.compile(r'social|footer', re.I))
        social_sections.extend(soup.find_all(['div', 'section'], attrs={'id': re.compile(r'social|footer', re.I)}))
        
        section_links = {}
        for section in social_sections:
            links = section.find_all('a', href=True)
            for link in links:
                href = link['href']
                for platform, patterns in social_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, href, re.IGNORECASE):
                            if not href.startswith('http'):
                                href = 'https://' + href
                            section_links[platform] = href
                            print(f"  ✅ {platform}: {href}")
                            break
        
        if not section_links:
            print("  ❌ No social media links found in footer/social sections")
        
        # Method 3: Look for any links containing social media domains
        print("\n🔍 Method 3: Searching all links for social media domains...")
        all_links = soup.find_all('a', href=True)
        domain_links = {}
        
        social_domains = ['instagram.com', 'linkedin.com', 'facebook.com', 'tiktok.com', 'twitter.com', 'x.com', 'youtube.com']
        
        for link in all_links:
            href = link['href']
            for domain in social_domains:
                if domain in href.lower():
                    platform = domain.split('.')[0]
                    if platform == 'x':
                        platform = 'twitter'
                    if not href.startswith('http'):
                        href = 'https://' + href
                    domain_links[platform] = href
                    print(f"  ✅ {platform}: {href}")
                    break
        
        if not domain_links:
            print("  ❌ No social media links found in any links")
        
        # Combine all methods
        all_found = {**found_links, **section_links, **domain_links}
        
        print(f"\n📊 Summary:")
        print(f"  Method 1 (Regex): {len(found_links)} links")
        print(f"  Method 2 (Sections): {len(section_links)} links")
        print(f"  Method 3 (All links): {len(domain_links)} links")
        print(f"  Total unique: {len(all_found)} links")
        
        if all_found:
            print(f"\n✅ Final social media links found:")
            for platform, url in all_found.items():
                print(f"    {platform}: {url}")
        else:
            print(f"\n❌ No social media links found with any method")
            
            # Debug: Show some sample links
            print(f"\n🔍 Sample links found on page (first 10):")
            sample_links = [link['href'] for link in all_links[:10] if link.get('href')]
            for i, link in enumerate(sample_links, 1):
                print(f"    {i}. {link}")
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return {}

def test_website_service_directly(url):
    """Test the website service directly."""
    print(f"\n🔍 Testing Website Service directly for: {url}")
    print("=" * 60)
    
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
        
        from app.services.website_service import WebsiteService
        
        website_service = WebsiteService()
        result = website_service.run(url)
        
        print("📊 Website Service Result:")
        print(json.dumps(result, indent=2))
        
        social_links = result.get('social_links', {})
        if social_links:
            print(f"\n✅ Website service found {len(social_links)} social links:")
            for platform, link in social_links.items():
                if link and link != "N/A":
                    print(f"    {platform}: {link}")
        else:
            print(f"\n❌ Website service found no social links")
        
        return social_links
        
    except Exception as e:
        print(f"❌ Website service error: {e}")
        return {}

def test_multiple_websites():
    """Test multiple websites to find one that works."""
    test_urls = [
        "https://coca-cola.com",
        "https://starbucks.com", 
        "https://airbnb.com",
        "https://spotify.com",
        "https://netflix.com"
    ]
    
    print("\n🧪 Testing Multiple Websites for Social Links")
    print("=" * 60)
    
    for url in test_urls:
        print(f"\n📍 Testing: {url}")
        try:
            links = test_social_link_extraction(url)
            if links:
                print(f"✅ SUCCESS: Found {len(links)} social links")
                return url, links
            else:
                print(f"❌ No links found")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None, {}

def main():
    """Run all tests."""
    print("🧪 Social Media Link Extraction Debug")
    print("=" * 60)
    
    # Test Nike first
    nike_links = test_social_link_extraction("https://nike.com")
    
    # Test website service
    service_links = test_website_service_directly("https://nike.com")
    
    if not nike_links and not service_links:
        print("\n⚠️  Nike.com didn't work. Testing other websites...")
        working_url, working_links = test_multiple_websites()
        
        if working_url:
            print(f"\n🎉 Found working website: {working_url}")
            print("💡 Use this URL for testing the unified competitor analysis:")
            print(f"   {working_url}")
        else:
            print("\n❌ No websites worked. This suggests:")
            print("   1. Internet connection issues")
            print("   2. All tested sites use JavaScript for social links")
            print("   3. Anti-bot protection")
            print("   4. Need to improve social link detection")

if __name__ == "__main__":
    main()
