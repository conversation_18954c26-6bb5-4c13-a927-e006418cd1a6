"""
Website Service for Lead Processing API.

Handles all website-related functionality including:
- Website analysis and scraping
- Business information extraction
- Website health checking
- Content analysis
"""

import os
import re
import json
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from dotenv import load_dotenv
from openai import OpenAI
from typing import Dict, Any, Optional

# Load environment variables
load_dotenv()


class WebsiteService:
    """Service for website analysis and scraping."""

    def __init__(self):
        """Initialize website service."""
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.is_available = True

    def scrape_website(self, url: str) -> Dict[str, Any]:
        """
        Scrape a business website and analyze its content.

        Args:
            url (str): The website URL to scrape

        Returns:
            dict: Dictionary containing the scraped business information, or an error message
        """
        # Normalize URL and extract info
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        domain = urlparse(url).netloc.replace('www.', '')
        company_name = domain.split('.')[0].replace('-', ' ').title()

        # Set up headers and make request
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract text content with better cleaning
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()

            # Remove common non-content elements
            for element in soup.find_all(['nav', 'footer', 'header', 'aside', 'menu']):
                element.decompose()

            # Focus on main content areas
            main_content = ""
            content_selectors = [
                'main', '[role="main"]', '.main-content', '#main-content',
                '.content', '#content', '.page-content', '.entry-content',
                'article', '.article', '.post-content', '.text-content'
            ]

            for selector in content_selectors:
                content_area = soup.select_one(selector)
                if content_area:
                    main_content = content_area.get_text()
                    break

            # If no main content area found, use body but filter better
            if not main_content:
                text = soup.get_text()
            else:
                text = main_content

            # Enhanced text cleaning
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk and len(chunk) > 3)

            # Remove common website noise
            noise_patterns = [
                r'cookie', r'privacy policy', r'terms of service', r'subscribe',
                r'newsletter', r'follow us', r'social media', r'copyright',
                r'all rights reserved', r'menu', r'navigation', r'skip to'
            ]

            for pattern in noise_patterns:
                text = re.sub(pattern, '', text, flags=re.IGNORECASE)

            # Clean up extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()

            print(f"🔍 Extracted text length: {len(text)} characters")
            print(f"📄 First 300 chars: {text[:300]}")

            # Limit text length for API
            if len(text) > 8000:
                text = text[:8000] + "..."

            # Extract social media links
            social_links = self._extract_social_links(soup, url)

            # Try to find about page
            about_url = self._find_about_page(soup, url)
            about_content = ""

            if about_url:
                try:
                    about_response = requests.get(about_url, headers=headers, timeout=5)
                    about_soup = BeautifulSoup(about_response.content, 'html.parser')
                    for script in about_soup(["script", "style"]):
                        script.decompose()
                    about_text = about_soup.get_text()
                    lines = (line.strip() for line in about_text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    about_content = ' '.join(chunk for chunk in chunks if chunk)[:2000]
                except:
                    about_content = ""

            # Analyze with OpenAI
            analysis = self._analyze_with_openai(text, about_content, domain, company_name)

            # Combine results
            result = {
                "company_name": company_name,
                "website": {
                    "main": url,
                    "about": about_url
                },
                "social_links": social_links,
                **analysis
            }

            return result

        except requests.exceptions.Timeout:
            return {"error": f"Request to {url} timed out"}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to fetch {url}: {str(e)}"}
        except Exception as e:
            return {"error": f"Error analyzing {url}: {str(e)}"}

    def _extract_social_links(self, soup: BeautifulSoup, base_url: str) -> Dict[str, str]:
        """Extract social media links from the webpage."""
        social_links = {}

        # Social media patterns
        patterns = {
            'facebook': [r'facebook\.com/[^/\s"\']+', r'fb\.com/[^/\s"\']+'],
            'instagram': [r'instagram\.com/[^/\s"\']+'],
            'linkedin': [r'linkedin\.com/company/[^/\s"\']+', r'linkedin\.com/in/[^/\s"\']+'],
            'twitter': [r'twitter\.com/[^/\s"\']+', r'x\.com/[^/\s"\']+'],
            'tiktok': [r'tiktok\.com/@[^/\s"\']+'],
            'youtube': [r'youtube\.com/[^/\s"\']+', r'youtu\.be/[^/\s"\']+']
        }

        # Get all links from the page
        links = soup.find_all('a', href=True)
        page_text = str(soup)

        for platform, platform_patterns in patterns.items():
            for pattern in platform_patterns:
                # Search in href attributes
                for link in links:
                    href = link['href']
                    if re.search(pattern, href, re.IGNORECASE):
                        if not href.startswith('http'):
                            href = f"https://{href}"
                        social_links[platform] = href
                        break

                # Search in page text if not found in links
                if platform not in social_links:
                    matches = re.findall(pattern, page_text, re.IGNORECASE)
                    if matches:
                        url = matches[0]
                        if not url.startswith('http'):
                            url = f"https://{url}"
                        social_links[platform] = url

        return social_links

    def _find_about_page(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """Find the about page URL."""
        about_keywords = ['about', 'about-us', 'about_us', 'company', 'who-we-are', 'our-story']

        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href'].lower()
            text = link.get_text().lower()

            if any(keyword in href or keyword in text for keyword in about_keywords):
                if href.startswith('http'):
                    return href
                elif href.startswith('/'):
                    return f"{base_url.rstrip('/')}{href}"
                else:
                    return f"{base_url.rstrip('/')}/{href}"

        return None

    def _analyze_with_openai(self, text: str, about_content: str, domain: str, company_name: str) -> Dict[str, Any]:
        """Analyze website content using OpenAI."""
        try:
            # Check if we have enough content
            total_content = f"{text} {about_content}".strip()
            if len(total_content) < 100:
                return {
                    "business_goals": [f"Insufficient content extracted from {company_name} website"],
                    "about_the_business": [f"Website content too short for analysis: {len(total_content)} characters"],
                    "target_market": ["Unable to determine from limited content"],
                    "products_services": ["Unable to determine from limited content"],
                    "unique_value_proposition": ["Unable to determine from limited content"],
                    "content_length": len(total_content)
                }

            prompt = f"""
            You are a business analyst. Analyze the following website content and extract specific business information.

            Company: {company_name}
            Website: {domain}

            MAIN PAGE CONTENT:
            {text[:4000]}

            ABOUT PAGE CONTENT:
            {about_content}

            Extract the following information and respond ONLY with valid JSON in this exact format:
            {{
                "business_goals": ["specific goal 1", "specific goal 2", "specific goal 3"],
                "about_the_business": ["what the company does", "company description", "business model"],
                "target_market": ["target customer 1", "target customer 2", "market segment"],
                "products_services": ["product/service 1", "product/service 2", "product/service 3"],
                "unique_value_proposition": ["unique advantage 1", "unique advantage 2", "differentiator"]
            }}

            Requirements:
            - Extract SPECIFIC information from the content provided
            - Each array should contain 2-5 specific items
            - Be factual and based only on the content provided
            - If information is not available, use "Not specified on website"
            - Respond with ONLY the JSON object, no other text
            """

            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )

            # Parse the response
            content = response.choices[0].message.content

            # Try to extract JSON from the response
            try:
                import json
                # Find JSON in the response
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end != 0:
                    json_str = content[start:end]
                    analysis = json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
            except Exception as json_error:
                # Fallback to basic analysis with debug info
                print(f"❌ JSON parsing failed: {json_error}")
                print(f"🔍 OpenAI response length: {len(content)} chars")
                print(f"🔍 First 500 chars of response: {content[:500]}")
                analysis = {
                    "business_goals": [f"JSON parsing failed for {company_name}"],
                    "about_the_business": [f"OpenAI responded but JSON parsing failed: {str(json_error)}"],
                    "target_market": ["Check OpenAI response format"],
                    "products_services": ["Check OpenAI response format"],
                    "unique_value_proposition": ["Check OpenAI response format"],
                    "raw_openai_response": content[:1000]  # First 1000 chars for debugging
                }

            return analysis

        except Exception as e:
            print(f"❌ OpenAI analysis failed: {e}")
            print(f"🔍 Error type: {type(e).__name__}")
            print(f"🔍 Text length sent to OpenAI: {len(text)} chars")
            return {
                "business_goals": [f"OpenAI analysis failed: {str(e)}"],
                "about_the_business": [f"Error analyzing {company_name}: {str(e)}"],
                "target_market": ["Analysis failed - check OpenAI API key"],
                "products_services": ["Analysis failed - check OpenAI API key"],
                "unique_value_proposition": ["Analysis failed - check OpenAI API key"],
                "error_details": str(e)
            }

    def run(self, url: str) -> Dict[str, Any]:
        """
        Main entry point for website scraping and analysis.

        Args:
            url (str): The website URL to analyze

        Returns:
            dict: Dictionary containing the scraped business information or an error message
        """
        if not url:
            error_msg = "Error: No URL provided. Exiting."
            return {"error": error_msg}

        return self.scrape_website(url)

    def analyze_website(self, url: str) -> Dict[str, Any]:
        """
        Analyze a website and extract business information.

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing analysis results
        """
        try:
            result = self.run(url)

            if "error" in result:
                return {
                    "url": url,
                    "business_info": self._get_basic_website_info(url),
                    "analysis_successful": False,
                    "error": result["error"]
                }
            else:
                return {
                    "url": url,
                    "business_info": result,
                    "analysis_successful": True,
                    "error": None
                }

        except Exception as e:
            return {
                "error": f"Website analysis failed: {str(e)}",
                "url": url,
                "business_info": self._get_basic_website_info(url),
                "analysis_successful": False
            }

    def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract specific business information from a website.

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing business information
        """
        try:
            result = self.run(url)

            if "error" in result:
                return {
                    "domain": "Error",
                    "business_goal": "Error",
                    "description": f"Failed to analyze website: {result['error']}",
                    "error": result["error"]
                }

            # Extract domain from URL
            domain = urlparse(url).netloc.replace('www.', '') if url else "Unknown"

            # Extract business goals and description
            business_goals = result.get("business_goals", ["Unknown"])
            about_business = result.get("about_the_business", ["No description available"])

            return {
                "domain": domain,
                "business_goal": business_goals[0] if business_goals else "Unknown",
                "description": about_business[0] if about_business else "No description available",
                "error": None
            }

        except Exception as e:
            return {
                "domain": "Error",
                "business_goal": "Error",
                "description": f"Failed to analyze website: {str(e)}",
                "error": str(e)
            }

    def check_website_health(self, url: str) -> Dict[str, Any]:
        """
        Check if a website is accessible and responsive.

        Args:
            url: Website URL to check

        Returns:
            Dict containing health check results
        """
        try:
            import requests
            from urllib.parse import urlparse

            # Ensure URL has a scheme
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            # Parse URL to get domain
            parsed = urlparse(url)
            domain = parsed.netloc or parsed.path

            # Make request with timeout
            response = requests.get(url, timeout=10, allow_redirects=True)

            return {
                "url": url,
                "domain": domain,
                "status_code": response.status_code,
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "accessible": response.status_code < 400,
                "redirected": len(response.history) > 0,
                "final_url": response.url,
                "error": None
            }

        except requests.exceptions.Timeout:
            return {
                "url": url,
                "domain": urlparse(url).netloc,
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": "Request timeout"
            }
        except requests.exceptions.ConnectionError:
            return {
                "url": url,
                "domain": urlparse(url).netloc,
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": "Connection error"
            }
        except Exception as e:
            return {
                "url": url,
                "domain": "Unknown",
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": str(e)
            }

    def get_website_metadata(self, url: str) -> Dict[str, Any]:
        """
        Extract metadata from a website (title, description, etc.).

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing website metadata
        """
        try:
            import requests
            from bs4 import BeautifulSoup

            # Ensure URL has a scheme
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            response = requests.get(url, timeout=5)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract metadata
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"

            # Meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '').strip() if meta_desc else "No description"

            # Meta keywords
            meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
            keywords = meta_keywords.get('content', '').strip() if meta_keywords else "No keywords"

            return {
                "url": url,
                "title": title_text,
                "description": description,
                "keywords": keywords,
                "status_code": response.status_code,
                "error": None
            }

        except Exception as e:
            return {
                "url": url,
                "title": "Error",
                "description": "Error",
                "keywords": "Error",
                "status_code": None,
                "error": str(e)
            }

    def is_service_available(self) -> bool:
        """
        Check if the website service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.is_available

    def _get_basic_website_info(self, url: str) -> Dict[str, Any]:
        """
        Get basic website information without AI analysis (fallback method).

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing basic website information
        """
        try:
            from urllib.parse import urlparse

            # Normalize URL
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            domain = urlparse(url).netloc.replace('www.', '')
            company_name = domain.split('.')[0].replace('-', ' ').title()

            # Get basic metadata with timeout handling
            try:
                metadata = self.get_website_metadata(url)
            except Exception:
                metadata = {"description": "Website metadata not available"}

            return {
                "company_name": company_name,
                "website": {
                    "main": url,
                    "about": None
                },
                "social_links": {},
                "business_goals": [f"Information extracted from {company_name} website"],
                "about_the_business": [metadata.get("description", "Business information not available")],
                "target_market": ["Target market information not available"],
                "products_services": ["Products and services information not available"],
                "unique_value_proposition": ["Unique value proposition not available"]
            }
        except Exception as e:
            return {
                "company_name": "Unknown",
                "website": {"main": url, "about": None},
                "social_links": {},
                "business_goals": ["Information not available"],
                "about_the_business": ["Information not available"],
                "target_market": ["Information not available"],
                "products_services": ["Information not available"],
                "unique_value_proposition": ["Information not available"]
            }

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get detailed service status information.

        Returns:
            Dict containing service status
        """
        return {
            "service": "website_service",
            "available": self.is_available,
            "module_imported": True,
            "capabilities": [
                "website_analysis",
                "business_info_extraction",
                "health_checking",
                "metadata_extraction"
            ] if self.is_available else [],
            "dependencies": {
                "openai": True,
                "requests": True,
                "beautifulsoup4": True
            }
        }
