import requests
import re
import json
from typing import Dict



class TikTokCollector:
    """
    A class to collect and process data from TikTok videos using RapidAPI.
    
    This class provides functionality to:
    - Extract video IDs from TikTok URLs
    - Fetch comments from TikTok videos
    - Format and structure the collected data
    
    Attributes:
        rapidapi_key (str): Authentication key for RapidAPI services
        rapidapi_host (str): Hostname for the TikTok API endpoint
        base_url (str): Complete base URL for API requests
        headers (dict): HTTP headers required for API requests
    """

    def __init__(self, rapidapi_key: str):
        """
        Initialize the TikTok collector with API credentials.
        
        Args:
            rapidapi_key (str): Your RapidAPI authentication key
        
        Sets up:
            - API host and base URL
            - Request headers with authentication
        """
        self.rapidapi_key = rapidapi_key
        self.rapidapi_host = "tiktok-video-feature-summary.p.rapidapi.com"
        self.base_url = f"https://{self.rapidapi_host}"
        self.headers = {
            'x-rapidapi-key': self.rapidapi_key,
            'x-rapidapi-host': self.rapidapi_host
        }

    def extract_video_id_from_url(self, url: str) -> str:
        """
        Extract the video ID from a TikTok URL.

        Args:
            url (str): Complete TikTok video URL

        Returns:
            str: Extracted video ID

        Raises:
            ValueError: If video ID cannot be extracted from the URL

        Supported URL formats:
            - tiktok.com/@username/video/{id}
            - tiktok.com/video/{id}
            - vm.tiktok.com/{shortcode}
            - vt.tiktok.com/{shortcode}
        """
        url = url.split('?')[0]
        patterns = [
            r'tiktok\.com/@[\w\.]+/video/(\d+)',
            r'tiktok\.com/video/(\d+)',
            r'vm\.tiktok\.com/(\w+)',
            r'vt\.tiktok\.com/(\w+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        raise ValueError("Could not extract video ID from URL")

    def get_post_comments(self, video_id: str, count: int) -> Dict:
        """
        Fetch comments for a specific TikTok video.

        Args:
            video_id (str): The unique identifier of the TikTok video
            count (int): Number of comments to retrieve

        Returns:
            Dict: JSON response containing video comments and metadata
                 Format: {
                     "data": {
                         "comments": [
                             {
                                 "text": str,
                                 "user": {
                                     "nickname": str,
                                     "unique_id": str
                                 }
                             },
                             ...
                         ]
                     }
                 }
        """
        endpoint = f"{self.base_url}/comment/list"
        params = {
            "url": f"https://www.tiktok.com/video/{video_id}",
            "count": str(count),
            "cursor": "0"
        }

        try:
            response = requests.get(endpoint, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error fetching comments: {e}")
            return {}

    def run(self, url: str, comment_count: int = 100) -> str:
        """
        Main execution method to process a TikTok video URL and extract formatted comments.

        Args:
            url (str): TikTok video URL to process
            comment_count (int, optional): Maximum number of comments to retrieve. Defaults to 100

        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "comment": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]

        Raises:
            Exception: If any error occurs during processing
        """
        try:
            video_id = self.extract_video_id_from_url(url)
            comments_data = self.get_post_comments(video_id, comment_count)
            
            formatted_comments = []
            for comment in comments_data.get("data", {}).get("comments", []):
                user = comment.get("user", {})
                formatted_comments.append({
                    "comment": comment.get("text"),
                    "commenter_name": user.get("nickname"),
                    "commenter_profile_link": f"https://www.tiktok.com/@{user.get('unique_id')}"
                })

            return json.dumps(formatted_comments, indent=2, ensure_ascii=False)
        except Exception as e:
            return str(e)