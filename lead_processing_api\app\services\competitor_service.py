"""
Competitor Service for Lead Processing API.

Handles all competitor-related functionality including:
- Competitor discovery and analysis
- Business domain analysis
- Competitor social media link extraction
- Market research automation
"""

import os
import json
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from openai import OpenAI
from .website_service import WebsiteService
from .social_media_collectors import InstagramCollector, LinkedInCollector, TiktokCollector
from ..models import Competitor, InstagramPost, InstagramComment

# Load environment variables
load_dotenv()


class CompetitorAnalyzer:
    """
    A class to analyze business competitors and their social media presence.

    This class takes a business website, domain, and goal as input, scrapes the website content,
    and uses OpenAI's GPT-4 to analyze and return competitor information including their
    social media presence across different platforms.

    Attributes:
        api_key (str): OpenAI API key for making API calls
        client (OpenAI): OpenAI client instance for interacting with OpenAI's API
    """

    def __init__(self):
        """Initialize the CompetitorAnalyzer with OpenAI API key."""
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")

        self.client = OpenAI(api_key=self.api_key)
        self.website_service = WebsiteService()

    def analyze_business(self, business_goal: str, scraped_website: str, domain: str) -> Dict[str, Any]:
        """
        Analyze business information and return competitor analysis.

        Args:
            business_goal (str): The business goal or mission
            scraped_website (str): Scraped content from the business website
            domain (str): Business domain/industry

        Returns:
            Dict: A dictionary containing:
                - business_analysis: Dict with primary_domain, specific_focus, and market_position
                - competitors: List of competitor dictionaries with name and social media links
        """
        analysis_prompt = f"""
        Analyze the following business information and provide detailed competitor analysis:

        Business Goal: {business_goal}
        Website Content: {scraped_website}
        Domain: {domain}

        Please provide a detailed analysis of 5 top competitors in this space, including:
        1. Social media links (Facebook, Instagram, LinkedIn, TikTok)
        2. Brief description of each competitor

        Format the response as a JSON object with this structure:
        {{
            "business_analysis": {{
                "primary_domain": "string",
                "specific_focus": "string",
                "market_position": "string"
            }},
            "competitors": [
                {{
                    "name": "Competitor Name",
                    "description": "Brief description",
                    "social_media": {{
                        "facebook": "https://facebook.com/page or N/A",
                        "instagram": "https://instagram.com/profile or N/A",
                        "linkedin": "https://linkedin.com/company/name or N/A",
                        "tiktok": "https://tiktok.com/@profile or N/A"
                    }}
                }}
            ]
        }}

        Ensure all social media links are complete URLs or "N/A" if not available.
        """

        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.3
            )

            content = response.choices[0].message.content

            # Extract JSON from response
            try:
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end != 0:
                    json_str = content[start:end]
                    results = json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
            except:
                # Fallback structure
                results = {
                    "business_analysis": {
                        "primary_domain": domain,
                        "specific_focus": business_goal,
                        "market_position": "Analysis not available"
                    },
                    "competitors": []
                }

            return results

        except Exception as e:
            print(f"Error in competitor analysis: {e}")
            return {
                "business_analysis": {
                    "primary_domain": domain,
                    "specific_focus": business_goal,
                    "market_position": "Analysis failed"
                },
                "competitors": []
            }

    def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract business domain and goal from a website URL using OpenAI.

        Args:
            url (str): The website URL to analyze

        Returns:
            Dict: Dictionary containing domain and business_goal
        """
        # Use website service to scrape content
        scraped_website = self.website_service.run(url)

        # Create a prompt for OpenAI to extract domain and business goal
        prompt = f"""
        Analyze the following website content and extract:
        1. The business domain/industry (e.g., agriculture, technology, healthcare)
        2. The business goal/mission

        Website URL: {url}
        Website Content: {scraped_website}

        Format the response as a JSON object with the following structure:
        {{
            "domain": "string",
            "business_goal": "string"
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )

            content = response.choices[0].message.content

            # Extract JSON from response
            try:
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end != 0:
                    json_str = content[start:end]
                    result = json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
            except:
                # Fallback
                result = {
                    "domain": "Unknown",
                    "business_goal": "Unknown"
                }

            return result

        except Exception as e:
            print(f"Error extracting business info: {e}")
            return {
                "domain": "Unknown",
                "business_goal": "Unknown"
            }

    def analyze_and_format(self, url: str, domain: str = None, business_goal: str = None) -> List[Dict[str, str]]:
        """
        Analyze a business website and return formatted competitor information.

        Args:
            url (str): The business website URL to analyze
            domain (str, optional): Business domain/industry. If None, will be extracted from website
            business_goal (str, optional): Business goal/mission. If None, will be extracted from website

        Returns:
            List[Dict]: List of dictionaries containing competitor information in the format:
            [
                {
                    "name": "Competitor Name",
                    "linkedin": "LinkedIn URL",
                    "instagram": "Instagram URL",
                    "facebook": "Facebook URL",
                    "tiktok": "TikTok URL"
                },
                ...
            ]
        """
        # Scrape website content
        scraped_website = self.website_service.run(url)

        # If domain or business_goal is not provided, extract them from the website
        if domain is None or business_goal is None:
            business_info = self.extract_business_info(url)
            domain = business_info.get("domain") if domain is None else domain
            business_goal = business_info.get("business_goal") if business_goal is None else business_goal

        # Get analysis
        results = self.analyze_business(business_goal, scraped_website, domain)

        # Format results into list of dictionaries
        competitors_list = []

        for competitor in results['competitors']:
            competitor_dict = {
                "name": competitor['name'],
                "linkedin": competitor['social_media']['linkedin'],
                "instagram": competitor['social_media']['instagram'],
                "facebook": competitor['social_media']['facebook'],
                "tiktok": competitor['social_media']['tiktok']
            }
            competitors_list.append(competitor_dict)

        return competitors_list


class CompetitorService:
    """Service for competitor analysis and discovery."""

    def __init__(self):
        """Initialize competitor service."""
        self.analyzer = CompetitorAnalyzer()
        self.website_service = WebsiteService()
        # Initialize social media collectors for comprehensive analysis
        self.instagram = InstagramCollector()
        self.linkedin = LinkedInCollector()
        self.tiktok = TiktokCollector()
        self.is_available = True
        self.website_available = True

    def analyze_competitors(
        self,
        website: str,
        business_domain: str,
        goals: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze and discover competitors for a business.

        Args:
            website: Business website URL
            business_domain: Business domain/industry
            goals: Optional business goals

        Returns:
            Dict containing competitor analysis results
        """
        try:
            # Get competitor analysis
            competitors_data = self.analyzer.analyze_and_format(
                website,
                business_domain,
                goals or "General business analysis"
            )

            return {
                "website": website,
                "business_domain": business_domain,
                "goals": goals,
                "competitors": competitors_data,
                "competitors_count": len(competitors_data),
                "analysis_successful": True,
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Competitor analysis failed: {str(e)}",
                "website": website,
                "business_domain": business_domain,
                "competitors": [],
                "analysis_successful": False
            }

    def extract_business_info(self, website: str) -> Dict[str, Any]:
        """
        Extract business information from a website.

        Args:
            website: Website URL to analyze

        Returns:
            Dict containing business information
        """
        try:
            result = self.analyzer.extract_business_info(website)

            return {
                "domain": result.get("domain", website),
                "business_goal": result.get("business_goal", "Unknown"),
                "description": result.get("description", "No description available"),
                "industry": result.get("domain", "Unknown"),
                "error": None
            }

        except Exception as e:
            return {
                "domain": website,
                "business_goal": "Error",
                "description": f"Failed to analyze business: {str(e)}",
                "industry": "Error",
                "error": str(e)
            }

    def find_competitors_by_domain(self, business_domain: str, limit: int = 10) -> Dict[str, Any]:
        """
        Find competitors based on business domain only.

        Args:
            business_domain: Business domain/industry
            limit: Maximum number of competitors to return

        Returns:
            Dict containing competitor list
        """
        try:
            # Analyze with domain focus
            competitors_data = self.analyzer.analyze_and_format(
                f"example-{business_domain.replace(' ', '-')}.com",
                business_domain,
                f"Find competitors in {business_domain} industry"
            )

            # Limit results
            limited_competitors = competitors_data[:limit] if competitors_data else []

            return {
                "business_domain": business_domain,
                "competitors": limited_competitors,
                "competitors_count": len(limited_competitors),
                "total_found": len(competitors_data),
                "limited_to": limit,
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Domain-based competitor analysis failed: {str(e)}",
                "business_domain": business_domain,
                "competitors": [],
                "competitors_count": 0
            }

    def get_competitor_social_links(self, competitor_name: str) -> Dict[str, Any]:
        """
        Get social media links for a specific competitor.

        Args:
            competitor_name: Name of the competitor

        Returns:
            Dict containing social media links
        """
        try:
            # This is a simplified approach - in a real implementation,
            # you might have a dedicated method for social link extraction
            competitors_data = self.analyzer.analyze_and_format(
                f"{competitor_name.lower().replace(' ', '')}.com",
                "general",
                f"Find social media for {competitor_name}"
            )

            # Extract social links from the first result
            if competitors_data and len(competitors_data) > 0:
                competitor = competitors_data[0]
                social_links = {
                    "linkedin": competitor.get("linkedin"),
                    "instagram": competitor.get("instagram"),
                    "facebook": competitor.get("facebook"),
                    "tiktok": competitor.get("tiktok")
                }

                # Filter out None/empty values
                social_links = {k: v for k, v in social_links.items() if v and v != "N/A"}

                return {
                    "competitor": competitor_name,
                    "social_links": social_links,
                    "links_found": len(social_links),
                    "error": None
                }
            else:
                return {
                    "competitor": competitor_name,
                    "social_links": {},
                    "links_found": 0,
                    "error": "No social links found"
                }

        except Exception as e:
            return {
                "error": f"Social link extraction failed: {str(e)}",
                "competitor": competitor_name,
                "social_links": {},
                "links_found": 0
            }

    def validate_competitor_data(self, competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean competitor data.

        Args:
            competitor_data: Raw competitor data

        Returns:
            Dict containing validated competitor data
        """
        try:
            # Required fields
            name = competitor_data.get("name", "Unknown Competitor")

            # Social media links with validation
            social_links = {}
            for platform in ["linkedin", "instagram", "facebook", "tiktok"]:
                link = competitor_data.get(platform)
                if link and link != "N/A" and link.startswith(("http://", "https://")):
                    social_links[platform] = link

            return {
                "name": name,
                "social_links": social_links,
                "valid": True,
                "validation_errors": []
            }

        except Exception as e:
            return {
                "name": competitor_data.get("name", "Unknown"),
                "social_links": {},
                "valid": False,
                "validation_errors": [str(e)]
            }

    def is_service_available(self) -> bool:
        """
        Check if the competitor service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.is_available

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get detailed service status information.

        Returns:
            Dict containing service status
        """
        return {
            "service": "competitor_service",
            "available": self.is_available,
            "website_analysis_available": self.website_available,
            "capabilities": [
                "competitor_discovery",
                "business_analysis",
                "social_link_extraction",
                "domain_based_search"
            ] if self.is_available else [],
            "dependencies": {
                "openai": True,
                "website_service": True
            }
        }

    def extract_business_info(self, website: str) -> Dict[str, Any]:
        """
        Extract business information from a website.

        Args:
            website: Website URL to analyze

        Returns:
            Dict containing business information
        """
        try:
            result = self.analyzer.extract_business_info(website)

            return {
                "domain": result.get("domain", website),
                "business_goal": result.get("business_goal", "Unknown"),
                "description": result.get("description", "No description available"),
                "industry": result.get("domain", "Unknown"),
                "error": None
            }

        except Exception as e:
            return {
                "domain": website,
                "business_goal": "Error",
                "description": f"Failed to analyze business: {str(e)}",
                "industry": "Error",
                "error": str(e)
            }

    def find_competitors_by_domain(self, business_domain: str, limit: int = 10) -> Dict[str, Any]:
        """
        Find competitors based on business domain only.

        Args:
            business_domain: Business domain/industry
            limit: Maximum number of competitors to return

        Returns:
            Dict containing competitor list
        """
        if not self.is_available:
            return {
                "error": "Competitor analysis service not available",
                "competitors": [],
                "domain": business_domain
            }

        try:
            # Analyze with domain focus
            competitors_data = self.analyzer.analyze_and_format(
                f"example-{business_domain.replace(' ', '-')}.com",
                business_domain,
                f"Find competitors in {business_domain} industry"
            )

            # Limit results
            limited_competitors = competitors_data[:limit] if competitors_data else []

            return {
                "business_domain": business_domain,
                "competitors": limited_competitors,
                "competitors_count": len(limited_competitors),
                "total_found": len(competitors_data),
                "limited_to": limit,
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Domain-based competitor analysis failed: {str(e)}",
                "business_domain": business_domain,
                "competitors": [],
                "competitors_count": 0
            }

    def get_competitor_social_links(self, competitor_name: str) -> Dict[str, Any]:
        """
        Get social media links for a specific competitor.

        Args:
            competitor_name: Name of the competitor

        Returns:
            Dict containing social media links
        """
        if not self.is_available:
            return {
                "error": "Competitor analysis service not available",
                "competitor": competitor_name,
                "social_links": {}
            }

        try:
            # This is a simplified approach - in a real implementation,
            # you might have a dedicated method for social link extraction
            competitors_data = self.analyzer.analyze_and_format(
                f"{competitor_name.lower().replace(' ', '')}.com",
                "general",
                f"Find social media for {competitor_name}"
            )

            # Extract social links from the first result
            if competitors_data and len(competitors_data) > 0:
                competitor = competitors_data[0]
                social_links = {
                    "linkedin": competitor.get("linkedin"),
                    "instagram": competitor.get("instagram"),
                    "facebook": competitor.get("facebook"),
                    "tiktok": competitor.get("tiktok")
                }

                # Filter out None/empty values
                social_links = {k: v for k, v in social_links.items() if v and v != "N/A"}

                return {
                    "competitor": competitor_name,
                    "social_links": social_links,
                    "links_found": len(social_links),
                    "error": None
                }
            else:
                return {
                    "competitor": competitor_name,
                    "social_links": {},
                    "links_found": 0,
                    "error": "No social links found"
                }

        except Exception as e:
            return {
                "error": f"Social link extraction failed: {str(e)}",
                "competitor": competitor_name,
                "social_links": {},
                "links_found": 0
            }

    def validate_competitor_data(self, competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean competitor data.

        Args:
            competitor_data: Raw competitor data

        Returns:
            Dict containing validated competitor data
        """
        try:
            # Required fields
            name = competitor_data.get("name", "Unknown Competitor")

            # Social media links with validation
            social_links = {}
            for platform in ["linkedin", "instagram", "facebook", "tiktok"]:
                link = competitor_data.get(platform)
                if link and link != "N/A" and link.startswith(("http://", "https://")):
                    social_links[platform] = link

            return {
                "name": name,
                "social_links": social_links,
                "valid": True,
                "validation_errors": []
            }

        except Exception as e:
            return {
                "name": competitor_data.get("name", "Unknown"),
                "social_links": {},
                "valid": False,
                "validation_errors": [str(e)]
            }

    def is_service_available(self) -> bool:
        """
        Check if the competitor service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.is_available

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get detailed service status information.

        Returns:
            Dict containing service status
        """
        return {
            "service": "competitor_service",
            "available": self.is_available,
            "website_analysis_available": self.website_available,
            "capabilities": [
                "competitor_discovery",
                "business_analysis",
                "social_link_extraction",
                "domain_based_search",
                "comprehensive_competitor_analysis",
                "social_media_post_collection",
                "instagram_comment_scraping"
            ] if self.is_available else [],
            "dependencies": {
                "openai": True,
                "website_service": True,
                "social_media_collectors": True
            }
        }

    def analyze_competitor_comprehensive(
        self,
        website_url: str,
        post_count: int = 10,
        comment_limit: int = 15,
        save_to_db: bool = False,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive competitor analysis from website URL.

        This method combines the functionality from the unified competitor service:
        1. Takes a competitor website URL
        2. Extracts social media links from the website
        3. Collects posts from all social media platforms
        4. Scrapes comments from Instagram posts
        5. Returns comprehensive analysis in one response

        Args:
            website_url: Competitor website URL
            post_count: Number of posts to collect per platform
            comment_limit: Number of comments to scrape per Instagram post
            save_to_db: Whether to save results to database
            db: Database session

        Returns:
            Dict containing comprehensive analysis with posts and Instagram comments
        """
        try:
            print(f"🔍 Starting comprehensive analysis for: {website_url}")

            # Step 1: Extract social media links from website
            print("📱 Extracting social media links...")
            social_links = self._extract_social_media_links(website_url)

            if not social_links:
                return {
                    "status": "error",
                    "message": "No social media links found on website",
                    "website_url": website_url,
                    "social_media_links": {},
                    "posts": {},
                    "instagram_comments": {},
                    "summary": self._empty_summary(),
                    "analysis_timestamp": "2024-01-01"
                }

            print(f"✅ Found social media links: {list(social_links.keys())}")

            # Step 2: Collect posts from all platforms
            print("📊 Collecting posts from all platforms...")
            all_posts = self._collect_all_platform_posts(social_links, post_count)

            # Step 3: Scrape Instagram comments
            instagram_comments = {}
            instagram_analysis = {}

            if "instagram" in social_links and all_posts.get("instagram", {}).get("posts"):
                print("💬 Scraping Instagram comments...")
                instagram_comments, instagram_analysis = self._scrape_instagram_comments(
                    all_posts["instagram"]["posts"],
                    comment_limit
                )

            # Step 4: Generate comprehensive summary
            summary = self._generate_summary(all_posts, instagram_comments)

            # Step 5: Save to database if requested
            if save_to_db and db:
                competitor_id = self._save_to_database(
                    website_url=website_url,
                    social_links=social_links,
                    posts=all_posts,
                    instagram_comments=instagram_comments,
                    db=db
                )
                summary["competitor_id"] = competitor_id

            print("✅ Comprehensive analysis completed!")

            return {
                "status": "success",
                "website_url": website_url,
                "social_media_links": social_links,
                "posts": all_posts,
                "instagram_comments": instagram_comments,
                "instagram_analysis": instagram_analysis,
                "summary": summary,
                "analysis_timestamp": "2024-01-01"
            }

        except Exception as e:
            print(f"❌ Comprehensive analysis failed: {str(e)}")
            return {
                "status": "error",
                "message": f"Analysis failed: {str(e)}",
                "website_url": website_url,
                "social_media_links": {},
                "posts": {},
                "instagram_comments": {},
                "summary": self._empty_summary(),
                "analysis_timestamp": "2024-01-01"
            }

    def _extract_social_media_links(self, website_url: str) -> Dict[str, str]:
        """Extract social media links from website."""
        try:
            # Use the website service to extract social media links
            result = self.website_service.run(website_url)

            if isinstance(result, dict) and "social_links" in result:
                social_links = result["social_links"]

                # Clean up the links - remove empty or "N/A" values
                cleaned_links = {}
                for platform, url in social_links.items():
                    if url and url != "N/A" and url.strip():
                        cleaned_links[platform] = url.strip()

                return cleaned_links

            return {}

        except Exception as e:
            print(f"⚠️  Error extracting social media links: {str(e)}")
            return {}

    def _collect_all_platform_posts(self, social_links: Dict[str, str], post_count: int) -> Dict[str, Any]:
        """Collect posts from all social media platforms."""
        all_posts = {}

        for platform, url in social_links.items():
            print(f"📱 Collecting {platform} posts from: {url}")

            try:
                if platform.lower() == "linkedin":
                    posts = self.linkedin.run(url)
                elif platform.lower() == "instagram":
                    posts = self.instagram.run(url, count=post_count)
                elif platform.lower() == "tiktok":
                    posts = self.tiktok.run(url, count=post_count)
                elif platform.lower() == "facebook":
                    # Facebook not implemented yet
                    posts = []
                else:
                    posts = []

                # Ensure posts is a list
                if not isinstance(posts, list):
                    posts = []

                all_posts[platform] = {
                    "platform_url": url,
                    "posts": posts,
                    "posts_count": len(posts),
                    "status": "success" if posts else "no_posts_found"
                }

                print(f"✅ {platform}: Found {len(posts)} posts")

            except Exception as e:
                print(f"❌ {platform} collection failed: {str(e)}")
                all_posts[platform] = {
                    "platform_url": url,
                    "posts": [],
                    "posts_count": 0,
                    "status": "error",
                    "error": str(e)
                }

        return all_posts

    def _scrape_instagram_comments(self, instagram_posts: List[str], comment_limit: int) -> tuple:
        """Scrape comments from Instagram posts."""
        instagram_comments = {}
        total_comments = 0

        for i, post_url in enumerate(instagram_posts, 1):
            print(f"💬 Scraping comments for post {i}/{len(instagram_posts)}: {post_url}")

            try:
                comment_result = self.instagram.scrape_post_comments(post_url, limit=comment_limit)

                if comment_result.get("status") == "success":
                    comments = comment_result.get("data", [])
                    instagram_comments[post_url] = {
                        "comments": comments,
                        "comment_count": len(comments),
                        "status": "success"
                    }
                    total_comments += len(comments)
                    print(f"  ✅ Found {len(comments)} comments")
                else:
                    instagram_comments[post_url] = {
                        "comments": [],
                        "comment_count": 0,
                        "status": "error",
                        "error": comment_result.get("message", "Unknown error")
                    }
                    print(f"  ❌ Comment scraping failed")

            except Exception as e:
                print(f"  ❌ Comment scraping error: {str(e)}")
                instagram_comments[post_url] = {
                    "comments": [],
                    "comment_count": 0,
                    "status": "error",
                    "error": str(e)
                }

        instagram_analysis = {
            "total_posts_analyzed": len(instagram_posts),
            "total_comments_collected": total_comments,
            "average_comments_per_post": total_comments / len(instagram_posts) if instagram_posts else 0
        }

        return instagram_comments, instagram_analysis

    def _generate_summary(self, all_posts: Dict[str, Any], instagram_comments: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary of the analysis."""
        platforms_analyzed = list(all_posts.keys())
        platforms_with_posts = [p for p, data in all_posts.items() if data.get("posts_count", 0) > 0]
        total_posts = sum(data.get("posts_count", 0) for data in all_posts.values())
        total_instagram_comments = sum(
            post_data.get("comment_count", 0)
            for post_data in instagram_comments.values()
        )

        return {
            "platforms_analyzed": platforms_analyzed,
            "platforms_with_posts": platforms_with_posts,
            "total_posts_collected": total_posts,
            "total_instagram_comments": total_instagram_comments,
            "instagram_posts_with_comments": len([
                post for post in instagram_comments.values()
                if post.get("comment_count", 0) > 0
            ]),
            "platform_breakdown": {
                platform: data.get("posts_count", 0)
                for platform, data in all_posts.items()
            }
        }

    def _empty_summary(self) -> Dict[str, Any]:
        """Return empty summary structure."""
        return {
            "platforms_analyzed": [],
            "platforms_with_posts": [],
            "total_posts_collected": 0,
            "total_instagram_comments": 0,
            "instagram_posts_with_comments": 0,
            "platform_breakdown": {}
        }

    def _save_to_database(
        self,
        website_url: str,
        social_links: Dict[str, str],
        posts: Dict[str, Any],
        instagram_comments: Dict[str, Any],
        db: Session
    ) -> int:
        """Save analysis results to database."""
        try:
            # Create competitor record
            competitor = Competitor(
                name=website_url.replace("https://", "").replace("http://", "").split("/")[0],
                linkedin=social_links.get("linkedin"),
                instagram=social_links.get("instagram"),
                facebook=social_links.get("facebook"),
                tiktok=social_links.get("tiktok"),
                job_id=1  # You might want to make this configurable
            )
            db.add(competitor)
            db.flush()

            # Save Instagram posts and comments
            if "instagram" in posts and posts["instagram"]["posts"]:
                for post_url in posts["instagram"]["posts"]:
                    instagram_post = InstagramPost(
                        competitor_id=competitor.id,
                        post_url=post_url
                    )
                    db.add(instagram_post)
                    db.flush()

                    # Save comments for this post
                    if post_url in instagram_comments:
                        comments_data = instagram_comments[post_url].get("comments", [])
                        for comment_data in comments_data:
                            comment = InstagramComment(
                                post_id=instagram_post.id,
                                username=comment_data.get("username", ""),
                                text=comment_data.get("text", ""),
                                likes_count=comment_data.get("likes_count", 0)
                            )
                            db.add(comment)

            db.commit()
            return competitor.id

        except Exception as e:
            print(f"❌ Database save failed: {str(e)}")
            db.rollback()
            return -1