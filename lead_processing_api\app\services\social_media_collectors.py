"""
Social Media Collectors Service

This service provides functionality to collect posts from various social media platforms
including LinkedIn, Instagram, TikTok, and Facebook.
"""

import os
import requests
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from apify_client import ApifyClient

# Load environment variables
load_dotenv()


class TiktokCollector:
    """
    A class to collect and analyze TikTok posts and profile information.

    This class uses the RapidAPI TikTok API to fetch posts from TikTok profiles.
    It handles URL parsing, API requests, and post extraction.

    Attributes:
        rapidapi_key (str): RapidAPI key for TikTok API access
        headers (dict): HTTP headers for API requests containing RapidAPI credentials
    """

    def __init__(self):
        self.rapidapi_key = os.getenv('TIKTOK_RAPID_API_KEY') or "4c415cc6a7msh8b25835d3aa3f01p161a68jsn27599b3dc377"
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "tiktok-scraper7.p.rapidapi.com"
        }

    def get_profile_posts(self, url: str, count: int = 5) -> List[str]:
        """
        Get posts from a TikTok profile.

        Args:
            url: TikTok profile URL
            count: Number of posts to retrieve

        Returns:
            List of post URLs
        """
        try:
            # Extract username from URL
            if "@" in url:
                username = url.split("@")[1].split("/")[0].split("?")[0]
            else:
                return []

            api_url = "https://tiktok-scraper7.p.rapidapi.com/user/posts"
            querystring = {"user_id": username, "count": str(count)}

            response = requests.get(api_url, headers=self.headers, params=querystring)

            if response.status_code == 200:
                data = response.json()
                posts = []

                if "data" in data and "videos" in data["data"]:
                    for video in data["data"]["videos"]:
                        if "video_id" in video:
                            post_url = self.construct_post_url(username, video["video_id"])
                            posts.append(post_url)

                return posts[:count]
            else:
                return []

        except Exception as e:
            print(f"Error getting TikTok posts: {e}")
            return []

    def construct_post_url(self, username: str, video_id: str) -> str:
        """
        Construct a TikTok post URL from a username and video ID.
        """
        return f"https://www.tiktok.com/@{username}/video/{video_id}"

    def run(self, url: str, count: int = 5) -> List[str]:
        """
        Main method to collect TikTok posts.

        Args:
            url: TikTok profile URL
            count: Number of posts to collect

        Returns:
            List of post URLs
        """
        posts = self.get_profile_posts(url, count=count)
        return posts if posts else []


class LinkedInCollector:
    """
    A class to collect and analyze LinkedIn company posts and profile information.

    This class uses the RapidAPI LinkedIn API to fetch posts and company information
    from LinkedIn company pages. It handles URL parsing, API requests, and post extraction.

    Attributes:
        rapidapi_key (str): RapidAPI key for LinkedIn API access
        headers (dict): HTTP headers for API requests containing RapidAPI credentials
    """

    def __init__(self):
        self.rapidapi_key = os.getenv('LINKEDIN_RAPID_API_KEY') or "4c415cc6a7msh8b25835d3aa3f01p161a68jsn27599b3dc377"
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "linkedin-data-api.p.rapidapi.com"
        }

    def get_company_posts(self, url: str) -> List[str]:
        """
        Get all post URLs from a LinkedIn company page.

        Args:
            url: LinkedIn company URL or name

        Returns:
            A list of post URLs
        """
        try:
            # Extract company name from URL if it's a URL
            company_name = url

            # Check if it's a LinkedIn URL
            if "linkedin.com" in url:
                # Extract company name from URL like https://www.linkedin.com/company/company-name
                if "company/" in url:
                    parts = url.split("company/")
                    if len(parts) > 1:
                        company_name = parts[1]
                        # Remove any trailing parameters or slashes
                        if '?' in company_name:
                            company_name = company_name.split('?')[0]
                        if '/' in company_name:
                            company_name = company_name.split('/')[0]

            api_url = "https://linkedin-data-api.p.rapidapi.com/get-company-posts"
            querystring = {"company_name": company_name}

            response = requests.get(api_url, headers=self.headers, params=querystring)

            if response.status_code == 200:
                data = response.json()
                posts = []

                if "data" in data and isinstance(data["data"], list):
                    for post in data["data"]:
                        if "post_url" in post:
                            posts.append(post["post_url"])

                return posts
            else:
                return []

        except Exception as e:
            print(f"Error getting LinkedIn company posts: {e}")
            return []

    def run(self, url: str) -> List[str]:
        """
        Main method to collect LinkedIn posts.

        Args:
            url: LinkedIn company URL

        Returns:
            List of post URLs
        """
        company_posts = self.get_company_posts(url)
        return company_posts if company_posts else []


class InstagramCollector:
    """
    A class to collect and analyze Instagram posts and profile information.

    This class uses both RapidAPI Instagram API and Apify to fetch posts and comments
    from Instagram accounts. It handles URL parsing, API requests, post extraction,
    and comment scraping.

    Attributes:
        api_key (str): RapidAPI key for Instagram API access
        apify_token (str): Apify API token for comment scraping
        post_details_api_host (str): API host for post details endpoints
        user_posts_api_host (str): API host for user posts endpoints
        post_details_headers (dict): HTTP headers for post details requests
        user_posts_headers (dict): HTTP headers for user posts requests
        apify_client (ApifyClient): Apify client for comment scraping
    """

    def __init__(self):
        self.api_key = os.getenv('INSTAGRAM_RAPID_API_KEY') or "4c415cc6a7msh8b25835d3aa3f01p161a68jsn27599b3dc377"
        self.apify_token = os.getenv('APIFY_API_TOKEN') or "**********************************************"

        # API hosts for different endpoints
        self.post_details_api_host = "instagram-scraper-api2.p.rapidapi.com"
        self.user_posts_api_host = "instagram-scraper-2022.p.rapidapi.com"

        # Headers for post details API
        self.post_details_headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.post_details_api_host
        }

        # Headers for user posts API
        self.user_posts_headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.user_posts_api_host
        }

        # Initialize Apify client for comment scraping
        self.apify_client = ApifyClient(self.apify_token)

    def scrape_post_comments(self, url: str, limit: int = 15) -> Dict[str, Any]:
        """
        Scrape comments from an Instagram post using Apify.

        Args:
            url: Instagram post URL
            limit: Maximum number of comments to scrape

        Returns:
            Dict containing status and comment data
        """
        try:
            print(f"Starting comment scraping for: {url}")

            # Run the Instagram Comments Scraper actor
            run_input = {
                "directUrls": [url],
                "resultsLimit": limit
            }

            run = self.apify_client.actor("apify/instagram-comment-scraper").call(run_input=run_input)

            print(f"Actor run completed. Dataset ID: {run['defaultDatasetId']}")

            # Collect results with specific fields
            results = []
            item_count = 0
            for item in self.apify_client.dataset(run["defaultDatasetId"]).iterate_items():
                item_count += 1
                comment_data = {
                    "text": item.get("text", ""),
                    "username": item.get("ownerUsername", ""),
                    "full_name": item.get("owner", {}).get("full_name", ""),
                    "profile_url": item.get("ownerProfilePicUrl", ""),
                    "timestamp": item.get("timestamp", ""),
                    "likes_count": item.get("likesCount", 0)
                }
                results.append(comment_data)

            print(f"Processed {item_count} comments from dataset")

            return {
                "status": "success",
                "data": results,
                "total_comments": len(results),
                "post_url": url
            }

        except Exception as e:
            print(f"Error scraping comments: {e}")
            return {
                "status": "error",
                "message": str(e),
                "data": [],
                "total_comments": 0,
                "post_url": url
            }

    def get_profile_posts(self, url: str, count: int = 10) -> List[str]:
        """
        Get posts from an Instagram profile.

        Args:
            url: Instagram profile URL
            count: Number of posts to retrieve

        Returns:
            List of post URLs
        """
        try:
            # Extract username from URL
            if "instagram.com/" in url:
                username = url.split("instagram.com/")[1].split("/")[0].split("?")[0]
            else:
                return []

            api_url = "https://instagram-scraper-2022.p.rapidapi.com/ig/posts_username/"
            querystring = {"username": username, "count": str(count)}

            response = requests.get(api_url, headers=self.user_posts_headers, params=querystring)

            if response.status_code == 200:
                data = response.json()
                posts = []

                if "data" in data and isinstance(data["data"], list):
                    for post in data["data"]:
                        if "shortcode" in post:
                            post_url = f"https://www.instagram.com/p/{post['shortcode']}/"
                            posts.append(post_url)

                return posts[:count]
            else:
                return []

        except Exception:
            return []

    def run(self, url: str, count: int = 10) -> List[str]:
        """
        Basic run method for backward compatibility - only collects posts
        """
        profile_posts = self.get_profile_posts(url, count)
        if profile_posts:
            return profile_posts
        else:
            return []

    def run_with_comments(self, url: str, post_count: int = 10, comment_limit: int = 15) -> Dict[str, Any]:
        """
        Comprehensive Instagram analysis - collects posts and comments.

        Args:
            url: Instagram profile URL
            post_count: Number of posts to collect
            comment_limit: Number of comments to scrape per post

        Returns:
            Dict containing posts, comments, and analysis summary
        """
        try:
            print(f"🔍 Starting comprehensive Instagram analysis for: {url}")

            # Step 1: Collect posts
            print(f"📱 Collecting {post_count} posts...")
            profile_posts = self.get_profile_posts(url, post_count)

            if not profile_posts:
                return {
                    "status": "error",
                    "message": "No posts found",
                    "profile_url": url,
                    "posts": [],
                    "comments": [],
                    "summary": {}
                }

            print(f"✓ Found {len(profile_posts)} posts")

            # Step 2: Scrape comments for each post
            posts_with_comments = []
            all_comments = []
            total_comments = 0
            posts_with_comments_count = 0

            for i, post_url in enumerate(profile_posts, 1):
                print(f"Processing post {i}/{len(profile_posts)}: {post_url}")

                # Scrape comments for this post
                comment_result = self.scrape_post_comments(post_url, limit=comment_limit)

                post_data = {
                    "url": post_url,
                    "position": i,
                    "comments_scraped": comment_result.get("status") == "success",
                    "comment_count": len(comment_result.get("data", [])),
                    "comments": comment_result.get("data", [])
                }

                posts_with_comments.append(post_data)

                # Add comments to overall collection
                if comment_result.get("status") == "success":
                    all_comments.extend(comment_result.get("data", []))
                    total_comments += len(comment_result.get("data", []))
                    posts_with_comments_count += 1

            # Step 3: Generate summary
            summary = {
                "total_posts": len(profile_posts),
                "total_comments": total_comments,
                "posts_with_comments": posts_with_comments_count,
                "average_comments_per_post": total_comments / len(profile_posts) if profile_posts else 0,
                "comment_collection_success_rate": posts_with_comments_count / len(profile_posts) if profile_posts else 0
            }

            print(f"✓ Analysis complete! Collected {total_comments} comments from {len(profile_posts)} posts")

            return {
                "status": "success",
                "profile_url": url,
                "posts": posts_with_comments,
                "comments": all_comments,
                "summary": summary,
                "analysis_timestamp": "2024-01-01"  # You could use datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ Instagram analysis failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "profile_url": url,
                "posts": [],
                "comments": [],
                "summary": {}
            }


# Backward compatibility aliases
Instagram = InstagramCollector
Tiktok = TiktokCollector
