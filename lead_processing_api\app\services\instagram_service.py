"""
Enhanced Instagram Service for Lead Processing API

This service provides comprehensive Instagram functionality including:
- Post collection from competitor profiles
- Comment scraping from individual posts
- Comprehensive analysis with engagement metrics
- Database integration for storing posts and comments
"""

import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

# Add the lead_processing directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))

try:
    from post_collector import Instagram as InstagramCollector
    INSTAGRAM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Instagram service: Could not import Instagram collector: {e}")
    INSTAGRAM_AVAILABLE = False
    InstagramCollector = None

from ..models import InstagramPost, InstagramComment, Competitor
from ..schemas import (
    InstagramComment as InstagramCommentSchema,
    InstagramPostWithComments,
    InstagramAnalysisSummary,
    InstagramEngagementAnalysis,
    InstagramContentInsights
)


class InstagramService:
    """
    Enhanced Instagram service with post collection and comment scraping capabilities.
    """
    
    def __init__(self):
        """Initialize the Instagram service."""
        self.is_available = INSTAGRAM_AVAILABLE
        if self.is_available:
            self.instagram_collector = InstagramCollector()
    
    def scrape_post_comments(
        self, 
        post_url: str, 
        limit: int = 10, 
        timeout: int = 120
    ) -> Dict[str, Any]:
        """
        Scrape comments from a single Instagram post.
        
        Args:
            post_url: Instagram post URL
            limit: Maximum number of comments to scrape
            timeout: Timeout for the scraping operation
            
        Returns:
            Dict containing scraped comments and metadata
        """
        if not self.is_available:
            return {
                "status": "error",
                "message": "Instagram service not available",
                "post_url": post_url,
                "comments": [],
                "total_comments": 0
            }
        
        try:
            result = self.instagram_collector.scrape_post_comments(
                url=post_url,
                limit=limit,
                timeout=timeout
            )
            
            # Convert to schema format
            comments = []
            for comment_data in result.get("data", []):
                comment = InstagramCommentSchema(
                    text=comment_data.get("text", ""),
                    username=comment_data.get("username", ""),
                    full_name=comment_data.get("full_name", ""),
                    profile_url=comment_data.get("profile_url", ""),
                    likes_count=comment_data.get("likes_count", 0),
                    timestamp=comment_data.get("timestamp", ""),
                    id=0,  # Will be set when saved to DB
                    post_id=0,  # Will be set when saved to DB
                    competitor_id=0,  # Will be set when saved to DB
                    created_at=datetime.now()
                )
                comments.append(comment)
            
            return {
                "status": result.get("status", "error"),
                "post_url": post_url,
                "comments": comments,
                "total_comments": len(comments),
                "message": result.get("message")
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Comment scraping failed: {str(e)}",
                "post_url": post_url,
                "comments": [],
                "total_comments": 0
            }
    
    def collect_posts(
        self, 
        profile_url: str, 
        count: int = 10
    ) -> Dict[str, Any]:
        """
        Collect posts from an Instagram profile.
        
        Args:
            profile_url: Instagram profile URL
            count: Number of posts to collect
            
        Returns:
            Dict containing collected posts
        """
        if not self.is_available:
            return {
                "status": "error",
                "message": "Instagram service not available",
                "posts": [],
                "total_posts": 0
            }
        
        try:
            posts = self.instagram_collector.run(profile_url, count=count)
            
            if isinstance(posts, list):
                return {
                    "status": "success",
                    "posts": posts,
                    "total_posts": len(posts),
                    "profile_url": profile_url
                }
            else:
                return {
                    "status": "error",
                    "message": str(posts),
                    "posts": [],
                    "total_posts": 0
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Post collection failed: {str(e)}",
                "posts": [],
                "total_posts": 0
            }
    
    def comprehensive_analysis(
        self,
        profile_url: str,
        post_count: int = 10,
        comment_limit: int = 10,
        save_to_db: bool = False,
        competitor_id: Optional[int] = None,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive Instagram analysis with posts and comments.
        
        Args:
            profile_url: Instagram profile URL
            post_count: Number of posts to collect
            comment_limit: Number of comments to scrape per post
            save_to_db: Whether to save results to database
            competitor_id: Competitor ID for database storage
            db: Database session
            
        Returns:
            Dict containing comprehensive analysis results
        """
        if not self.is_available:
            return {
                "status": "error",
                "message": "Instagram service not available",
                "profile_url": profile_url,
                "posts": [],
                "summary": self._empty_summary(),
                "analysis_timestamp": datetime.now().isoformat()
            }
        
        try:
            # Use the enhanced Instagram functionality
            result = self.instagram_collector.run_with_comments(
                url=profile_url,
                post_count=post_count,
                comment_limit=comment_limit
            )
            
            if result["status"] != "success":
                return {
                    "status": "error",
                    "message": result.get("message", "Analysis failed"),
                    "profile_url": profile_url,
                    "posts": [],
                    "summary": self._empty_summary(),
                    "analysis_timestamp": datetime.now().isoformat()
                }
            
            # Convert to API schema format
            posts_with_comments = []
            for post_data in result.get("posts", []):
                # Convert comments to schema format
                comments = []
                for comment_data in post_data.get("comments", []):
                    comment = InstagramCommentSchema(
                        text=comment_data.get("text", ""),
                        username=comment_data.get("username", ""),
                        full_name=comment_data.get("full_name", ""),
                        profile_url=comment_data.get("profile_url", ""),
                        likes_count=comment_data.get("likes_count", 0),
                        timestamp=comment_data.get("timestamp", ""),
                        id=0,
                        post_id=0,
                        competitor_id=0,
                        created_at=datetime.now()
                    )
                    comments.append(comment)
                
                post_with_comments = InstagramPostWithComments(
                    url=post_data.get("url", ""),
                    position=post_data.get("position", 0),
                    comments_scraped=post_data.get("comments_scraped", False),
                    comment_count=post_data.get("comment_count", 0),
                    comments=comments
                )
                posts_with_comments.append(post_with_comments)
            
            # Create summary
            summary_data = result.get("summary", {})
            summary = InstagramAnalysisSummary(
                total_posts=summary_data.get("total_posts", 0),
                total_comments=summary_data.get("total_comments", 0),
                posts_with_comments=summary_data.get("posts_with_comments", 0),
                average_comments_per_post=summary_data.get("average_comments_per_post", 0.0),
                comment_collection_success_rate=summary_data.get("comment_collection_success_rate", 0.0)
            )
            
            # Generate additional analysis
            engagement_analysis = self._analyze_engagement(posts_with_comments)
            content_insights = self._analyze_content(posts_with_comments)
            
            # Save to database if requested
            if save_to_db and db and competitor_id:
                self._save_to_database(
                    posts_with_comments=posts_with_comments,
                    competitor_id=competitor_id,
                    db=db
                )
            
            return {
                "status": "success",
                "profile_url": profile_url,
                "posts": posts_with_comments,
                "summary": summary,
                "engagement_analysis": engagement_analysis,
                "content_insights": content_insights,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Comprehensive analysis failed: {str(e)}",
                "profile_url": profile_url,
                "posts": [],
                "summary": self._empty_summary(),
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _analyze_engagement(self, posts: List[InstagramPostWithComments]) -> InstagramEngagementAnalysis:
        """Analyze engagement patterns from posts and comments."""
        total_comments = sum(post.comment_count for post in posts)
        
        # Find most engaging posts
        engaging_posts = sorted(posts, key=lambda x: x.comment_count, reverse=True)[:3]
        most_engaging = [
            {
                "url": post.url,
                "comment_count": post.comment_count,
                "position": post.position
            }
            for post in engaging_posts
        ]
        
        return InstagramEngagementAnalysis(
            total_engagement=total_comments,
            average_comments_per_post=total_comments / len(posts) if posts else 0,
            most_engaging_posts=most_engaging
        )
    
    def _analyze_content(self, posts: List[InstagramPostWithComments]) -> InstagramContentInsights:
        """Analyze content patterns from comments."""
        all_comments = []
        for post in posts:
            all_comments.extend(post.comments)
        
        total_comments = len(all_comments)
        unique_commenters = len(set(comment.username for comment in all_comments))
        
        # Find most active commenters
        commenter_counts = {}
        for comment in all_comments:
            username = comment.username
            commenter_counts[username] = commenter_counts.get(username, 0) + 1
        
        top_commenters = sorted(commenter_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        top_commenters_list = [
            {"username": username, "comment_count": count}
            for username, count in top_commenters
        ]
        
        return InstagramContentInsights(
            total_comments=total_comments,
            unique_commenters=unique_commenters,
            engagement_diversity=unique_commenters / total_comments if total_comments > 0 else 0,
            top_commenters=top_commenters_list
        )
    
    def _save_to_database(
        self,
        posts_with_comments: List[InstagramPostWithComments],
        competitor_id: int,
        db: Session
    ) -> None:
        """Save posts and comments to database."""
        try:
            for post_data in posts_with_comments:
                # Create Instagram post record
                instagram_post = InstagramPost(
                    competitor_id=competitor_id,
                    post_url=post_data.url,
                    comments_scraped=post_data.comments_scraped,
                    scraped_comments_count=post_data.comment_count
                )
                db.add(instagram_post)
                db.flush()  # Get the post ID
                
                # Create comment records
                for comment in post_data.comments:
                    instagram_comment = InstagramComment(
                        post_id=instagram_post.id,
                        competitor_id=competitor_id,
                        text=comment.text,
                        username=comment.username,
                        full_name=comment.full_name,
                        profile_url=comment.profile_url,
                        likes_count=comment.likes_count,
                        timestamp=comment.timestamp
                    )
                    db.add(instagram_comment)
            
            db.commit()
            
        except Exception as e:
            db.rollback()
            raise Exception(f"Database save failed: {str(e)}")
    
    def _empty_summary(self) -> InstagramAnalysisSummary:
        """Return empty summary for error cases."""
        return InstagramAnalysisSummary(
            total_posts=0,
            total_comments=0,
            posts_with_comments=0,
            average_comments_per_post=0.0,
            comment_collection_success_rate=0.0
        )
