from json import J<PERSON>NDecodeError
import http.client
import json
from typing import Dict, Any
from urllib.parse import urlparse


class InstagramCollector:
    """
    A class to collect and process data from Instagram posts using RapidAPI.
    
    This class provides functionality to:
    - Convert Instagram shortcodes to media IDs
    - Fetch comments from Instagram posts and reels
    - Format and structure the collected data
    
    Attributes:
        comment_api_key (str): Authentication key for RapidAPI services
        base_host (str): Base hostname for the Instagram scraper API
        headers (dict): HTTP headers required for API requests
    """

    def __init__(self, rapidapi_key: str):
        """
        Initialize the Instagram collector with API credentials.
        
        Args:
            rapidapi_key (str): Your RapidAPI authentication key
        
        Sets up:
            - Base host for API endpoints
            - Request headers with authentication
        """
        self.comment_api_key = rapidapi_key
        self.base_host = "instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com"
        self.headers = {
            'x-rapidapi-host': self.base_host,
            'x-rapidapi-key': rapidapi_key
        }

    def get_post_details(self, post_url: str) -> Dict | Exception | JSONDecodeError:
        """
        Fetch comments and metadata from an Instagram post or reel.

        Args:
            post_url (str): Complete URL to the Instagram post or reel

        Returns:
            Dict: JSON response containing post comments and metadata
                 Format: {
                     "comments": [
                         {
                             "text": str,
                             "user": {
                                 "full_name": str,
                                 "username": str
                             }
                         },
                         ...
                     ]
                 }

        Raises:
            JSONDecodeError: If response cannot be parsed as JSON
            Exception: For other errors during data fetching
        """
        try:
            shortcode = self._extract_shortcode(post_url)
            media_id = self._shortcode_to_id(shortcode)

            conn = http.client.HTTPSConnection(self.base_host)
            conn.request("GET", f"/comments_by_media_id?id={media_id}", headers=self.headers)
            res = conn.getresponse()
            data = res.read()

            return json.loads(data.decode("utf-8"))
        except json.JSONDecodeError as e:
            return e
        except Exception as e:
            return e

    def _shortcode_to_id(self, shortcode: str) -> int:
        """
        Convert an Instagram shortcode to its numeric media ID.

        Args:
            shortcode (str): Instagram post shortcode (e.g., 'ABC123')

        Returns:
            int: Numeric media ID used by Instagram's API

        Note:
            Uses Instagram's base64 variant algorithm for conversion
        """
        alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
        media_id = 0
        for char in shortcode:
            media_id = media_id * 64 + alphabet.index(char)
        return media_id

    def _extract_shortcode(self, url: str) -> str:
        """
        Extract the shortcode from an Instagram URL.

        Args:
            url (str): Complete Instagram post or reel URL

        Returns:
            str: Extracted shortcode

        Raises:
            ValueError: If shortcode cannot be extracted from the URL

        Example URLs supported:
            - instagram.com/p/{shortcode}
            - instagram.com/reel/{shortcode}
        """
        try:
            path_parts = urlparse(url).path.strip('/').split('/')
            if len(path_parts) >= 2:
                return path_parts[1]
            raise ValueError("Invalid Instagram URL format.")
        except Exception as e:
            raise ValueError(f"Error extracting shortcode: {str(e)}")

    def run(self, url: str) -> str:
        """
        Main execution method to process an Instagram post URL and extract formatted comments.

        Args:
            url (str): Instagram post or reel URL to process

        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "text": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]

        Raises:
            Exception: If any error occurs during processing
        """
        try:
            post_details = self.get_post_details(url)
            
            formatted_comments = []
            for comment in post_details.get("comments", []):
                user = comment.get("user", {})
                formatted_comments.append({
                    "text": comment.get("text", ""),
                    "commenter_name": user.get("full_name", ""),
                    "commenter_profile_link": f"https://instagram.com/{user.get('username', '')}"
                })

            return json.dumps(formatted_comments, indent=2, ensure_ascii=False)
        except Exception as e:
            return str(e)