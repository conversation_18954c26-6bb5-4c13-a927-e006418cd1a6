from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    JSON,
    Enum as S<PERSON><PERSON><PERSON>,
    Boolean
)
from sqlalchemy.orm import relationship
from .database import Base
from datetime import datetime, timezone
import enum

class JobStatus(enum.Enum):
    """Enum for job status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class User(Base):
    """
    Model for API users.

    Stores user authentication information including username,
    email, and hashed password for JWT authentication.
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationship to jobs (users can create jobs)
    jobs = relationship("Job", back_populates="user")

class Job(Base):
    """
    Model for lead processing jobs.

    Stores information about each lead processing request including
    business domain, website, goals, and processing status.
    """
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    business_domain = Column(String(255), nullable=False)
    website = Column(String(500), nullable=False)
    goals = Column(Text, nullable=True)
    email = Column(String(255), nullable=False)
    status = Column(SQLEnum(JobStatus), default=JobStatus.PENDING, nullable=False)
    created_at = Column(DateTime, default=datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))

    # Store the complete result data as JSON
    result_data = Column(JSON, nullable=True)

    # Error message if job failed
    error_message = Column(Text, nullable=True)

    # Relationships
    user = relationship("User", back_populates="jobs")
    competitors = relationship("Competitor", back_populates="job", cascade="all, delete-orphan")

class Competitor(Base):
    """
    Model for competitor information.

    Stores competitor details including name and social media links
    discovered during the analysis process.
    """
    __tablename__ = "competitors"

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey("jobs.id"), nullable=False)
    name = Column(String(255), nullable=False)
    linkedin = Column(String(500), nullable=True)
    instagram = Column(String(500), nullable=True)
    facebook = Column(String(500), nullable=True)
    tiktok = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    job = relationship("Job", back_populates="competitors")
    social_posts = relationship("SocialPost", back_populates="competitor", cascade="all, delete-orphan")

    # Platform-specific relationships
    linkedin_posts = relationship("LinkedInPost", back_populates="competitor", cascade="all, delete-orphan")
    instagram_posts = relationship("InstagramPost", back_populates="competitor", cascade="all, delete-orphan")
    facebook_posts = relationship("FacebookPost", back_populates="competitor", cascade="all, delete-orphan")
    tiktok_posts = relationship("TikTokPost", back_populates="competitor", cascade="all, delete-orphan")

class SocialPost(Base):
    """
    Model for social media posts.

    Stores individual social media posts collected from competitors
    across different platforms.
    """
    __tablename__ = "social_posts"

    id = Column(Integer, primary_key=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    platform = Column(String(50), nullable=False)  # linkedin, instagram, facebook, tiktok
    post_url = Column(String(1000), nullable=False)
    content = Column(Text, nullable=True)  # Post content if available
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    competitor = relationship("Competitor", back_populates="social_posts")


class LinkedInPost(Base):
    """
    Model for LinkedIn posts.

    Stores individual LinkedIn posts collected from competitors.
    """
    __tablename__ = "linkedin_posts"

    id = Column(Integer, primary_key=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    post_url = Column(String(1000), nullable=False)
    content = Column(Text, nullable=True)  # Post content if available
    likes_count = Column(Integer, nullable=True)  # LinkedIn-specific field
    comments_count = Column(Integer, nullable=True)  # LinkedIn-specific field
    shares_count = Column(Integer, nullable=True)  # LinkedIn-specific field
    post_type = Column(String(50), nullable=True)  # article, post, video, etc.
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    competitor = relationship("Competitor", back_populates="linkedin_posts")


class InstagramPost(Base):
    """
    Model for Instagram posts.

    Stores individual Instagram posts collected from competitors.
    """
    __tablename__ = "instagram_posts"

    id = Column(Integer, primary_key=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    post_url = Column(String(1000), nullable=False)
    content = Column(Text, nullable=True)  # Post content if available
    likes_count = Column(Integer, nullable=True)  # Instagram-specific field
    comments_count = Column(Integer, nullable=True)  # Instagram-specific field
    media_type = Column(String(50), nullable=True)  # photo, video, carousel, reel, story
    hashtags = Column(Text, nullable=True)  # Hashtags used in the post
    comments_scraped = Column(Boolean, default=False)  # Whether comments were scraped
    scraped_comments_count = Column(Integer, nullable=True)  # Number of comments actually scraped
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    competitor = relationship("Competitor", back_populates="instagram_posts")
    comments = relationship("InstagramComment", back_populates="post", cascade="all, delete-orphan")


class InstagramComment(Base):
    """
    Model for Instagram post comments.

    Stores individual comments scraped from Instagram posts.
    """
    __tablename__ = "instagram_comments"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("instagram_posts.id"), nullable=False)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    text = Column(Text, nullable=False)  # Comment text
    username = Column(String(255), nullable=False)  # Commenter username
    full_name = Column(String(255), nullable=True)  # Commenter full name
    profile_url = Column(String(1000), nullable=True)  # Commenter profile picture URL
    likes_count = Column(Integer, nullable=True)  # Number of likes on the comment
    timestamp = Column(String(100), nullable=True)  # Comment timestamp from Instagram
    created_at = Column(DateTime, default=datetime.now(timezone.utc))  # When we scraped it

    # Relationships
    post = relationship("InstagramPost", back_populates="comments")
    competitor = relationship("Competitor")


class FacebookPost(Base):
    """
    Model for Facebook posts.

    Stores individual Facebook posts collected from competitors.
    """
    __tablename__ = "facebook_posts"

    id = Column(Integer, primary_key=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    post_url = Column(String(1000), nullable=False)
    content = Column(Text, nullable=True)  # Post content if available
    likes_count = Column(Integer, nullable=True)  # Facebook-specific field
    comments_count = Column(Integer, nullable=True)  # Facebook-specific field
    shares_count = Column(Integer, nullable=True)  # Facebook-specific field
    reactions_count = Column(Integer, nullable=True)  # Total reactions (like, love, etc.)
    post_type = Column(String(50), nullable=True)  # status, photo, video, link, etc.
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    competitor = relationship("Competitor", back_populates="facebook_posts")


class TikTokPost(Base):
    """
    Model for TikTok posts.

    Stores individual TikTok posts collected from competitors.
    """
    __tablename__ = "tiktok_posts"

    id = Column(Integer, primary_key=True, index=True)
    competitor_id = Column(Integer, ForeignKey("competitors.id"), nullable=False)
    post_url = Column(String(1000), nullable=False)
    content = Column(Text, nullable=True)  # Post content if available
    likes_count = Column(Integer, nullable=True)  # TikTok-specific field
    comments_count = Column(Integer, nullable=True)  # TikTok-specific field
    shares_count = Column(Integer, nullable=True)  # TikTok-specific field
    views_count = Column(Integer, nullable=True)  # TikTok-specific field
    hashtags = Column(Text, nullable=True)  # Hashtags used in the post
    music_title = Column(String(255), nullable=True)  # Background music/sound
    video_duration = Column(Integer, nullable=True)  # Duration in seconds
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

    # Relationships
    competitor = relationship("Competitor", back_populates="tiktok_posts")
