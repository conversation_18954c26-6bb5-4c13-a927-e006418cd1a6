#!/usr/bin/env python3
"""
Test script to verify the unified competitor analysis fix
"""

import requests
import json
import sys

def test_unified_competitor_endpoint():
    """Test the unified competitor analysis endpoint."""
    
    # API endpoint
    base_url = "http://127.0.0.1:8000"
    endpoint = f"{base_url}/social-posts/competitor/analyze"
    
    # Test payload
    payload = {
        "website_url": "https://openai.com",  # Use a website that works
        "post_count": 3,
        "comment_limit": 5,
        "save_to_db": False
    }
    
    print("🧪 Testing Unified Competitor Analysis Endpoint")
    print("=" * 60)
    print(f"📍 Endpoint: {endpoint}")
    print(f"📦 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        print("\n📤 Sending request...")
        response = requests.post(endpoint, json=payload, timeout=300)
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            
            try:
                data = response.json()
                print("\n📊 Response Data Structure:")
                print(f"   Status: {data.get('status')}")
                print(f"   Website URL: {data.get('website_url')}")
                print(f"   Social Media Links: {len(data.get('social_media_links', {}))}")
                print(f"   Posts: {len(data.get('posts', {}))}")
                print(f"   Instagram Comments: {len(data.get('instagram_comments', {}))}")
                
                # Check summary structure
                summary = data.get('summary', {})
                print(f"\n📈 Summary:")
                print(f"   Platforms Analyzed: {summary.get('platforms_analyzed')} (type: {type(summary.get('platforms_analyzed'))})")
                print(f"   Platforms With Posts: {summary.get('platforms_with_posts')} (type: {type(summary.get('platforms_with_posts'))})")
                print(f"   Total Posts: {summary.get('total_posts_collected')}")
                print(f"   Instagram Comments: {summary.get('total_instagram_comments')}")
                
                # Validate data types
                platforms_analyzed = summary.get('platforms_analyzed')
                platforms_with_posts = summary.get('platforms_with_posts')
                
                if isinstance(platforms_analyzed, int) and isinstance(platforms_with_posts, int):
                    print("\n✅ Schema validation should pass - correct data types!")
                    return True
                else:
                    print(f"\n❌ Schema validation will fail:")
                    print(f"   platforms_analyzed: expected int, got {type(platforms_analyzed)}")
                    print(f"   platforms_with_posts: expected int, got {type(platforms_with_posts)}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response.text[:500]}")
                return False
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
        print("💡 Start the server with: uvicorn app.main:app --reload")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_server_health():
    """Test if the server is running."""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except:
        print("❌ Server is not running")
        return False

def main():
    """Run the test."""
    print("🚀 Unified Competitor Analysis Fix Test")
    print("=" * 60)
    
    # Check server health first
    if not test_server_health():
        print("\n💡 Please start the server first:")
        print("   cd lead_processing_api")
        print("   uvicorn app.main:app --reload")
        sys.exit(1)
    
    # Test the endpoint
    success = test_unified_competitor_endpoint()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test PASSED! The schema validation issue is fixed.")
        print("✅ The unified competitor analysis endpoint should work now.")
    else:
        print("❌ Test FAILED! There are still issues to resolve.")
        print("💡 Check the error messages above for details.")

if __name__ == "__main__":
    main()
