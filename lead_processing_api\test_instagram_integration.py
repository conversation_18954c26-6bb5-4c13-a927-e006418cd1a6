#!/usr/bin/env python3
"""
Test script for Instagram Integration in Lead Processing API

This script tests the new Instagram functionality including:
- Comment scraping from individual posts
- Comprehensive Instagram analysis
- Database integration
- API endpoint functionality
"""

import requests
import json
import time
from typing import Dict, Any


class InstagramAPITester:
    """Test class for Instagram API endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the tester with API base URL"""
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """Test if the API is running"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ API health check passed")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API health check failed: {str(e)}")
            return False
    
    def test_social_posts_health(self) -> bool:
        """Test social posts module health"""
        try:
            response = self.session.get(f"{self.base_url}/social-posts/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Social posts health: {data}")
                return True
            else:
                print(f"❌ Social posts health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Social posts health check failed: {str(e)}")
            return False
    
    def test_basic_instagram_posts(self) -> bool:
        """Test basic Instagram post collection"""
        print("\n🧪 Testing basic Instagram post collection...")
        
        payload = {
            "platform": "instagram",
            "url": "https://www.instagram.com/johndeere/",
            "count": 3
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/social-posts/instagram",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Basic Instagram posts: Found {data['total_posts']} posts")
                if data['total_posts'] > 0:
                    print(f"   Sample post: {data['posts'][0]}")
                return True
            else:
                print(f"❌ Basic Instagram posts failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Basic Instagram posts failed: {str(e)}")
            return False
    
    def test_instagram_comment_scraping(self) -> bool:
        """Test Instagram comment scraping from a single post"""
        print("\n🧪 Testing Instagram comment scraping...")
        
        payload = {
            "post_url": "https://www.instagram.com/p/DJtyFcRM-Xn/",
            "limit": 5,
            "timeout": 120
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/social-posts/instagram/comments",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Comment scraping: {data['status']}")
                print(f"   Total comments: {data['total_comments']}")
                
                if data['total_comments'] > 0:
                    sample_comment = data['comments'][0]
                    print(f"   Sample comment: @{sample_comment['username']}: {sample_comment['text'][:50]}...")
                
                return data['status'] == 'success'
            else:
                print(f"❌ Comment scraping failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Comment scraping failed: {str(e)}")
            return False
    
    def test_comprehensive_instagram_analysis(self) -> bool:
        """Test comprehensive Instagram analysis"""
        print("\n🧪 Testing comprehensive Instagram analysis...")
        
        payload = {
            "profile_url": "https://www.instagram.com/johndeere/",
            "post_count": 2,
            "comment_limit": 3,
            "save_to_db": False
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/social-posts/instagram/comprehensive",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Comprehensive analysis: {data['status']}")
                
                if data['status'] == 'success':
                    summary = data['summary']
                    print(f"   📊 Summary:")
                    print(f"      - Total posts: {summary['total_posts']}")
                    print(f"      - Total comments: {summary['total_comments']}")
                    print(f"      - Posts with comments: {summary['posts_with_comments']}")
                    print(f"      - Average comments per post: {summary['average_comments_per_post']:.1f}")
                    print(f"      - Success rate: {summary['comment_collection_success_rate']:.1%}")
                    
                    if data.get('engagement_analysis'):
                        engagement = data['engagement_analysis']
                        print(f"   📈 Engagement Analysis:")
                        print(f"      - Total engagement: {engagement['total_engagement']}")
                        print(f"      - Most engaging posts: {len(engagement['most_engaging_posts'])}")
                    
                    if data.get('content_insights'):
                        insights = data['content_insights']
                        print(f"   🔍 Content Insights:")
                        print(f"      - Unique commenters: {insights['unique_commenters']}")
                        print(f"      - Engagement diversity: {insights['engagement_diversity']:.2f}")
                        print(f"      - Top commenters: {len(insights['top_commenters'])}")
                
                return data['status'] == 'success'
            else:
                print(f"❌ Comprehensive analysis failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Comprehensive analysis failed: {str(e)}")
            return False
    
    def test_api_documentation(self) -> bool:
        """Test if API documentation includes new endpoints"""
        print("\n🧪 Testing API documentation...")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                social_media = data.get('available_endpoints', {}).get('social_media', {})
                
                required_endpoints = [
                    'scrape_instagram_comments',
                    'comprehensive_instagram_analysis'
                ]
                
                missing_endpoints = []
                for endpoint in required_endpoints:
                    if endpoint not in social_media:
                        missing_endpoints.append(endpoint)
                
                if not missing_endpoints:
                    print("✅ API documentation includes new Instagram endpoints")
                    return True
                else:
                    print(f"❌ Missing endpoints in documentation: {missing_endpoints}")
                    return False
            else:
                print(f"❌ API documentation check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API documentation check failed: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results"""
        print("🚀 Starting Instagram API Integration Tests")
        print("=" * 60)
        
        tests = {
            "health_check": self.test_health_check,
            "social_posts_health": self.test_social_posts_health,
            "basic_instagram_posts": self.test_basic_instagram_posts,
            "instagram_comment_scraping": self.test_instagram_comment_scraping,
            "comprehensive_analysis": self.test_comprehensive_instagram_analysis,
            "api_documentation": self.test_api_documentation
        }
        
        results = {}
        
        for test_name, test_func in tests.items():
            try:
                results[test_name] = test_func()
                time.sleep(1)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test {test_name} crashed: {str(e)}")
                results[test_name] = False
        
        # Print summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "✅ PASS" if passed_test else "❌ FAIL"
            print(f"{status} - {test_name}")
            if passed_test:
                passed += 1
        
        print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Instagram integration is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")
        
        return results


def main():
    """Main function to run the tests"""
    print("Instagram API Integration Test Suite")
    print("This script tests the enhanced Instagram functionality in the Lead Processing API")
    print()
    
    # Check if API is running
    tester = InstagramAPITester()
    
    if not tester.test_health_check():
        print("\n❌ API is not running. Please start the API server first:")
        print("   cd lead_processing_api")
        print("   uvicorn app.main:app --reload")
        return
    
    # Run all tests
    results = tester.run_all_tests()
    
    # Provide next steps
    print("\n📚 NEXT STEPS:")
    if all(results.values()):
        print("✅ Integration is complete and working!")
        print("   • Use the new endpoints in your applications")
        print("   • Check the API documentation at http://localhost:8000/docs")
        print("   • Run database migrations if needed")
    else:
        print("⚠️  Fix the failing tests:")
        for test_name, passed in results.items():
            if not passed:
                print(f"   • Fix: {test_name}")
        print("   • Check API logs for detailed error messages")
        print("   • Verify all dependencies are installed")


if __name__ == "__main__":
    main()
