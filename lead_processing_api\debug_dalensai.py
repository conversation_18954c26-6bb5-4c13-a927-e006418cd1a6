#!/usr/bin/env python3
"""
Debug script specifically for Dalensai website
"""

import requests
from bs4 import BeautifulSoup
import json

def test_dalensai_direct():
    """Test direct access to Dalensai website."""
    url = "https://dalensai.com"
    
    print(f"🔍 Testing direct access to: {url}")
    print("=" * 60)
    
    # Enhanced headers
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none"
    }
    
    try:
        session = requests.Session()
        session.headers.update(headers)
        
        print("📤 Making request...")
        response = session.get(url, timeout=15, allow_redirects=True)
        
        print(f"✅ Status Code: {response.status_code}")
        print(f"📏 Content Length: {len(response.content)} bytes")
        print(f"📄 Content Type: {response.headers.get('content-type', 'unknown')}")
        print(f"🔗 Final URL: {response.url}")
        print(f"🔄 Redirects: {len(response.history)} redirects")
        
        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Get raw text
        raw_text = soup.get_text()
        print(f"📝 Raw text length: {len(raw_text)} characters")
        
        # Show first 500 characters
        print("\n📄 First 500 characters of content:")
        print("-" * 50)
        print(repr(raw_text[:500]))
        print("-" * 50)
        
        # Check for common JavaScript frameworks
        html_content = str(soup)
        js_indicators = [
            'react', 'angular', 'vue', 'next.js', 'nuxt',
            'spa', 'single-page', 'javascript', 'js-',
            'app.js', 'bundle.js', 'main.js'
        ]
        
        found_js = [indicator for indicator in js_indicators if indicator in html_content.lower()]
        if found_js:
            print(f"\n🔍 JavaScript framework indicators found: {found_js}")
        
        # Check meta tags
        title = soup.find('title')
        if title:
            print(f"\n📋 Title: {title.get_text().strip()}")
        
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            print(f"📋 Meta Description: {meta_desc.get('content', '')}")
        
        # Look for main content areas
        content_areas = []
        selectors = ['main', '[role="main"]', '.main-content', '#main-content', '.content', '#content']
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                content_areas.append((selector, len(element.get_text())))
        
        if content_areas:
            print(f"\n📦 Content areas found:")
            for selector, length in content_areas:
                print(f"   {selector}: {length} characters")
        else:
            print("\n⚠️  No main content areas found")
        
        # Check if it's a SPA (Single Page Application)
        if len(raw_text) < 100 and any(indicator in html_content.lower() for indicator in ['react', 'vue', 'angular', 'spa']):
            print("\n🔍 DIAGNOSIS: This appears to be a Single Page Application (SPA)")
            print("   - Content is loaded dynamically with JavaScript")
            print("   - Basic web scraping won't work")
            print("   - Need browser automation (Selenium) or API access")
        
        return response, soup, raw_text
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None, None

def test_alternative_urls():
    """Test alternative URLs that might have more content."""
    base_urls = [
        "https://dalensai.com",
        "https://www.dalensai.com",
        "http://dalensai.com",
        "https://dalensai.com/about",
        "https://dalensai.com/home"
    ]
    
    print("\n🔍 Testing alternative URLs:")
    print("=" * 60)
    
    for url in base_urls:
        try:
            response = requests.get(url, timeout=10, allow_redirects=True)
            soup = BeautifulSoup(response.content, 'html.parser')
            text_length = len(soup.get_text())
            
            print(f"📍 {url}")
            print(f"   Status: {response.status_code}")
            print(f"   Text length: {text_length} chars")
            print(f"   Final URL: {response.url}")
            
            if text_length > 100:
                print(f"   ✅ Good content found!")
                return url, response, soup
            else:
                print(f"   ⚠️  Limited content")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None, None, None

def main():
    """Run all diagnostic tests."""
    print("🧪 Dalensai Website Diagnostic")
    print("=" * 60)
    
    # Test main URL
    response, soup, raw_text = test_dalensai_direct()
    
    if raw_text and len(raw_text) > 100:
        print("\n✅ Main URL has good content!")
    else:
        print("\n⚠️  Main URL has limited content. Testing alternatives...")
        alt_url, alt_response, alt_soup = test_alternative_urls()
        
        if alt_url:
            print(f"\n✅ Found better content at: {alt_url}")
        else:
            print("\n❌ All URLs have limited content")
            print("\n💡 Recommendations:")
            print("   1. Website uses JavaScript rendering (SPA)")
            print("   2. Consider using Selenium for browser automation")
            print("   3. Look for API endpoints")
            print("   4. Try different websites for testing")
            print("   5. Contact website owner for scraping permission")

if __name__ == "__main__":
    main()
