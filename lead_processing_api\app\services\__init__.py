"""
Services package for Lead Processing API.

This package contains all business logic services organized by functionality:
- email_service: Email notification handling
- website_service: Website analysis and scraping
- competitor_service: Competitor analysis and discovery
- social_media_collectors: Social media post collection (self-contained)
- job_service: Background job processing and management
"""

from .email_service import EmailService
from .website_service import WebsiteService
from .competitor_service import CompetitorService
from .social_media_collectors import InstagramCollector, LinkedInCollector, TiktokCollector
from .job_service import JobService

# Create service instances
email_service = EmailService()
website_service = WebsiteService()
competitor_service = CompetitorService()
job_service = JobService()

# Social media collectors (not singleton instances)
instagram_collector = InstagramCollector
linkedin_collector = LinkedInCollector
tiktok_collector = TiktokCollector

# Legacy compatibility
class LeadProcessingService:
    """Legacy wrapper for the new JobService."""

    def __init__(self):
        self.job_service = job_service

    def process_job(self, job_id: int):
        """Process a job using the new job service."""
        return self.job_service.process_job(job_id)

# Global service instance for backward compatibility
lead_processing_service = LeadProcessingService()

__all__ = [
    "EmailService",
    "WebsiteService",
    "CompetitorService",
    "InstagramCollector",
    "LinkedInCollector",
    "TiktokCollector",
    "JobService",
    "LeadProcessingService",
    "email_service",
    "website_service",
    "competitor_service",
    "instagram_collector",
    "linkedin_collector",
    "tiktok_collector",
    "job_service",
    "lead_processing_service"
]
