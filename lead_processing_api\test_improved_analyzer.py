#!/usr/bin/env python3
"""
Test the improved website analyzer
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Load environment variables
load_dotenv()

def test_improved_analyzer(url="https://openai.com"):
    """Test the improved website analyzer."""
    print(f"🧪 Testing Improved Website Analyzer for: {url}")
    print("=" * 60)

    try:
        from app.services.website_service import WebsiteService

        website_service = WebsiteService()
        result = website_service.run(url)

        print("📊 Analysis Result:")
        print(json.dumps(result, indent=2))

        # Check if we got real data
        business_goals = result.get("business_goals", [])
        if business_goals:
            first_goal = business_goals[0]
            if any(keyword in first_goal.lower() for keyword in ["insufficient", "failed", "error", "json parsing"]):
                print("\n❌ Still getting fallback responses")
                print(f"🔍 Issue: {first_goal}")
                return False
            else:
                print("\n✅ Got real business analysis!")
                return True
        else:
            print("\n❌ No business goals found")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_multiple_websites():
    """Test multiple websites to see improvement."""
    test_urls = [
        "https://openai.com",
        "https://microsoft.com",
        "https://apple.com",
        "https://github.com",
        "https://dalensai.com"  # Test this last since it might be problematic
    ]

    print("🧪 Testing Multiple Websites")
    print("=" * 60)

    results = {}
    for url in test_urls:
        print(f"\n🔍 Testing: {url}")
        try:
            from app.services.website_service import WebsiteService
            website_service = WebsiteService()
            result = website_service.run(url)

            # Check quality of results
            business_goals = result.get("business_goals", [])
            if business_goals and not any(keyword in business_goals[0].lower() for keyword in ["insufficient", "failed", "error"]):
                results[url] = "✅ SUCCESS"
                print(f"  ✅ Got real data: {business_goals[0][:100]}...")
            else:
                results[url] = "❌ FAILED"
                print(f"  ❌ Fallback response: {business_goals[0] if business_goals else 'No data'}")

        except Exception as e:
            results[url] = f"❌ ERROR: {e}"
            print(f"  ❌ Error: {e}")

    print("\n" + "=" * 60)
    print("📊 Summary:")
    for url, status in results.items():
        print(f"  {url}: {status}")

    success_count = sum(1 for status in results.values() if "SUCCESS" in status)
    print(f"\n🎯 Success Rate: {success_count}/{len(test_urls)} ({success_count/len(test_urls)*100:.1f}%)")

if __name__ == "__main__":
    print("🚀 Testing Improved Website Analyzer")
    print("=" * 60)

    # Test single website
    success = test_improved_analyzer()

    if success:
        print("\n🎉 Single test passed! Testing multiple websites...")
        test_multiple_websites()
    else:
        print("\n⚠️  Single test failed. Check the issues above.")
        print("\n💡 Common fixes:")
        print("   - Check OpenAI API key is valid")
        print("   - Ensure internet connection is working")
        print("   - Verify website is accessible")
