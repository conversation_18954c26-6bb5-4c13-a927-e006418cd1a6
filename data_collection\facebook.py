import requests
import json
from typing import Dict, List, Optional
from datetime import datetime
import re

class FacebookDataGatherer:
    """
    A class to collect and process data from Facebook posts using RapidAPI.
    
    This class provides functionality to:
    - Extract post IDs from Facebook URLs
    - Fetch comments from Facebook posts
    - Format and structure the collected data
    
    Attributes:
        rapidapi_key (str): Authentication key for RapidAPI services
        base_url (str): Base endpoint URL for the Facebook scraper API
        headers (dict): HTTP headers required for API requests
    """

    def __init__(self, rapidapi_key: str):
        """
        Initialize the Facebook data gatherer with API credentials.
        
        Args:
            rapidapi_key (str): Your RapidAPI authentication key
        
        Sets up:
            - Base URL for API endpoints
            - Request headers with authentication
        """
        self.rapidapi_key = rapidapi_key
        self.base_url = "https://facebook-scraper3.p.rapidapi.com"
        self.headers = {
            "x-rapidapi-host": "facebook-scraper3.p.rapidapi.com",
            "x-rapidapi-key": rapidapi_key
        }

    def get_post_comments(self, post_id: str, limit: int = 100) -> Dict:
        """
        Retrieve comments from a specific Facebook post.

        Args:
            post_id (str): The unique identifier of the Facebook post
            limit (int, optional): Maximum number of comments to retrieve. Defaults to 100

        Returns:
            Dict: JSON response containing post comments and related metadata
                 Format: {
                     "results": [
                         {
                             "message": str,
                             "author": {
                                 "name": str,
                                 "url": str
                             }
                         },
                         ...
                     ]
                 }

        Raises:
            Exception: If there's an error fetching comments from the API
        """
        endpoint = f"{self.base_url}/post/comments"
        params = {
            "post_id": post_id,
            "limit": limit
        }

        try:
            response = requests.get(endpoint, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error fetching comments: {str(e)}")

    @staticmethod
    def extract_post_id_from_url(url: str) -> str:
        """
        Parse a Facebook URL to extract the post ID.

        Args:
            url (str): Complete Facebook post URL

        Returns:
            str: Extracted post ID

        Raises:
            ValueError: If post ID cannot be extracted from the URL

        Supported URL formats:
            - facebook.com/{username}/posts/{id}
            - facebook.com/photo.php?fbid={id}
            - facebook.com/permalink.php?story_fbid={id}
            - facebook.com/groups/{group}/posts/{id}
            - facebook.com/{username}/videos/{id}
            - facebook.com/{username}/activity/{id}
            - facebook.com/{username}/photos/{id}
        """
        url = url.split('?')[0]
        patterns = [
            r'facebook\.com/.+/posts/(\d+)',
            r'facebook\.com/photo\.php.*fbid=(\d+)',
            r'facebook\.com/permalink\.php.*story_fbid=(\d+)',
            r'facebook\.com/groups/.+/posts/(\d+)',
            r'facebook\.com/.+/videos/(\d+)',
            r'facebook\.com/.+/activity/(\d+)',
            r'facebook\.com/.+/photos/(\d+)',
            r'facebook\.com/share/p/(\w+)',
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        parts = [p for p in url.split('/') if p]
        for part in reversed(parts):
            if part.isdigit():
                return part
        raise ValueError("Could not extract post ID from the provided URL.")

    def run(self, url: str, limit: int = 100) -> str:
        """
        Main execution method to process a Facebook post URL and extract formatted comments.

        Args:
            url (str): Facebook post URL to process
            limit (int, optional): Maximum number of comments to retrieve. Defaults to 100

        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "text": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]

        Raises:
            Exception: If any error occurs during processing
        """
        try:
            post_id = self.extract_post_id_from_url(url)
            comments_data = self.get_post_comments(post_id, limit=limit)

            formatted_comments = []
            for comment in comments_data.get("results", []):
                author = comment.get("author", {})
                formatted_comments.append({
                    "text": comment.get("message"),
                    "commenter_name": author.get("name"),
                    "commenter_profile_link": author.get("url"),
                })

            return json.dumps(formatted_comments, indent=2, ensure_ascii=False)
        except Exception as e:
            return str(e)