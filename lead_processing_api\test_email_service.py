#!/usr/bin/env python3
"""
Test script for Email Service

This script tests the email service functionality to ensure it's properly configured.
"""

import os
import sys
from datetime import datetime, timezone

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.email_service import EmailService
from app import models


def test_email_configuration():
    """Test if email service is properly configured."""
    print("🔧 Testing Email Service Configuration...")
    
    email_service = EmailService()
    
    print(f"📧 SMTP Server: {email_service.smtp_server}")
    print(f"📧 SMTP Port: {email_service.smtp_port}")
    print(f"📧 Username: {email_service.smtp_username}")
    print(f"📧 From Email: {email_service.from_email}")
    print(f"📧 Is Configured: {email_service.is_configured}")
    
    if not email_service.is_configured:
        print("\n❌ Email service is NOT configured!")
        print("Please set the following environment variables in your .env file:")
        print("- SMTP_USERNAME (your email address)")
        print("- SMTP_PASSWORD (your app password)")
        return False
    
    print("\n✅ Email service is configured!")
    return True


def test_welcome_email():
    """Test sending a welcome email."""
    print("\n📧 Testing Welcome Email...")
    
    email_service = EmailService()
    
    if not email_service.is_configured:
        print("❌ Email service not configured - skipping test")
        return False
    
    # Get recipient email (use the same as sender for testing)
    test_email = email_service.smtp_username
    
    try:
        success = email_service.send_welcome_email(
            to_email=test_email,
            username="Test User"
        )
        
        if success:
            print(f"✅ Welcome email sent successfully to {test_email}")
            return True
        else:
            print("❌ Failed to send welcome email")
            return False
            
    except Exception as e:
        print(f"❌ Welcome email test failed: {e}")
        return False


def test_job_completion_email():
    """Test sending a job completion email."""
    print("\n📧 Testing Job Completion Email...")
    
    email_service = EmailService()
    
    if not email_service.is_configured:
        print("❌ Email service not configured - skipping test")
        return False
    
    # Create mock job and competitors for testing
    mock_job = type('MockJob', (), {
        'id': 123,
        'business_domain': 'Agriculture Technology',
        'website': 'https://example.com',
        'goals': 'Test lead processing',
        'updated_at': datetime.now(timezone.utc)
    })()
    
    mock_competitor = type('MockCompetitor', (), {
        'name': 'Test Competitor',
        'linkedin': 'https://linkedin.com/company/test',
        'instagram': 'https://instagram.com/test',
        'facebook': 'N/A',
        'tiktok': 'N/A',
        'social_posts': ['post1', 'post2', 'post3']  # Mock posts
    })()
    
    mock_competitors = [mock_competitor]
    
    # Get recipient email (use the same as sender for testing)
    test_email = email_service.smtp_username
    
    try:
        success = email_service.send_job_completion_email(
            to_email=test_email,
            job=mock_job,
            competitors=mock_competitors
        )
        
        if success:
            print(f"✅ Job completion email sent successfully to {test_email}")
            return True
        else:
            print("❌ Failed to send job completion email")
            return False
            
    except Exception as e:
        print(f"❌ Job completion email test failed: {e}")
        return False


def test_error_notification_email():
    """Test sending an error notification email."""
    print("\n📧 Testing Error Notification Email...")
    
    email_service = EmailService()
    
    if not email_service.is_configured:
        print("❌ Email service not configured - skipping test")
        return False
    
    # Create mock job for testing
    mock_job = type('MockJob', (), {
        'id': 456,
        'business_domain': 'Test Domain',
        'website': 'https://test.com',
        'goals': 'Test error notification',
        'updated_at': datetime.now(timezone.utc)
    })()
    
    # Get recipient email (use the same as sender for testing)
    test_email = email_service.smtp_username
    
    try:
        success = email_service.send_error_notification(
            to_email=test_email,
            job=mock_job,
            error_message="This is a test error message for email testing purposes."
        )
        
        if success:
            print(f"✅ Error notification email sent successfully to {test_email}")
            return True
        else:
            print("❌ Failed to send error notification email")
            return False
            
    except Exception as e:
        print(f"❌ Error notification email test failed: {e}")
        return False


def main():
    """Run all email service tests."""
    print("🧪 Email Service Test Suite")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_email_configuration()
    
    if not config_ok:
        print("\n❌ Email service configuration failed!")
        print("\n📋 Setup Instructions:")
        print("1. Go to your Google Account settings")
        print("2. Enable 2-Factor Authentication")
        print("3. Generate an App Password for Mail")
        print("4. Update SMTP_PASSWORD in your .env file with the app password")
        print("5. Run this test again")
        return
    
    # Run email tests
    tests = [
        ("Welcome Email", test_welcome_email),
        ("Job Completion Email", test_job_completion_email),
        ("Error Notification Email", test_error_notification_email)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All email tests passed! Email service is working correctly.")
        print("📧 Check your inbox for the test emails.")
    else:
        print("\n⚠️  Some email tests failed. Check the error messages above.")
        print("💡 Common issues:")
        print("   - Incorrect app password")
        print("   - 2FA not enabled on Gmail")
        print("   - Firewall blocking SMTP")
        print("   - Gmail security settings")


if __name__ == "__main__":
    main()
