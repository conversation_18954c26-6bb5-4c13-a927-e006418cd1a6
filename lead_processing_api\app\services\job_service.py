"""
Job Service for Lead Processing API.

Handles all job-related functionality including:
- Background job processing
- Job status management
- Complete lead processing workflows
- Integration with other services
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from .. import models
from ..database import <PERSON>Local
from .email_service import EmailService
from .website_service import WebsiteService
from .competitor_service import CompetitorService
from .social_media_collectors import InstagramCollector, LinkedInCollector, TiktokCollector


class JobService:
    """Service for managing and processing lead generation jobs."""

    def __init__(self):
        """Initialize job service with other services."""
        self.email_service = EmailService()
        self.website_service = WebsiteService()
        self.competitor_service = CompetitorService()
        # Initialize social media collectors
        self.instagram_collector = InstagramCollector()
        self.linkedin_collector = LinkedInCollector()
        self.tiktok_collector = TiktokCollector()

    def process_job(self, job_id: int) -> None:
        """
        Process a lead generation job in the background.

        Args:
            job_id: ID of the job to process
        """
        db = SessionLocal()
        try:
            # Get the job
            job = db.query(models.Job).filter(models.Job.id == job_id).first()
            if not job:
                print(f"❌ Job {job_id} not found")
                return

            print(f"🚀 Starting job processing for job {job_id}")

            # Update job status to processing
            job.status = models.JobStatus.PROCESSING
            job.updated_at = datetime.now(timezone.utc)
            db.commit()

            # Process the job
            self._run_lead_processing(job, db)

        except Exception as e:
            print(f"❌ Job {job_id} failed: {e}")
            # Update job status to failed
            job = db.query(models.Job).filter(models.Job.id == job_id).first()
            if job:
                job.status = models.JobStatus.FAILED
                job.error_message = str(e)
                job.updated_at = datetime.now(timezone.utc)
                db.commit()

                # Send error notification
                self.email_service.send_error_notification(job.email, job, str(e))
        finally:
            db.close()

    def _run_lead_processing(self, job: models.Job, db: Session) -> None:
        """
        Run the actual lead processing logic.

        Args:
            job: Job instance to process
            db: Database session
        """
        try:
            print(f"📊 Processing job {job.id}: {job.business_domain} - {job.website}")

            # Step 1: Analyze the website (optional)
            website_analysis = None
            if self.website_service.is_service_available():
                print("🌐 Analyzing website...")
                website_analysis = self.website_service.analyze_website(job.website)

            # Step 2: Find competitors
            if not self.competitor_service.is_service_available():
                raise Exception("Competitor analysis service not available")

            print("🔍 Finding competitors...")
            competitor_analysis = self.competitor_service.analyze_competitors(
                job.website,
                job.business_domain,
                job.goals
            )

            if not competitor_analysis.get("analysis_successful"):
                raise Exception(f"Competitor analysis failed: {competitor_analysis.get('error')}")

            competitors_data = competitor_analysis.get("competitors", [])
            print(f"✅ Found {len(competitors_data)} competitors")

            # Step 3: Store competitors in database
            competitors = []
            for comp_data in competitors_data:
                competitor = models.Competitor(
                    job_id=job.id,
                    name=comp_data.get('name', 'Unknown'),
                    linkedin=comp_data.get('linkedin'),
                    instagram=comp_data.get('instagram'),
                    facebook=comp_data.get('facebook'),
                    tiktok=comp_data.get('tiktok')
                )
                db.add(competitor)
                db.flush()  # Get the ID
                competitors.append(competitor)

                # Step 4: Collect social media posts for each competitor
                print(f"📱 Collecting social posts for {competitor.name}...")
                self._collect_social_posts(competitor, comp_data, db)

            # Step 5: Update job status to completed
            job.status = models.JobStatus.COMPLETED
            job.updated_at = datetime.now(timezone.utc)
            # Calculate total posts from all platform-specific tables
            total_posts = self._calculate_total_posts_for_job(job.id, db)

            job.result_data = {
                "competitors_count": len(competitors),
                "website_analysis": website_analysis,
                "processing_completed_at": job.updated_at.isoformat(),
                "total_social_posts": total_posts
            }
            db.commit()

            print(f"✅ Job {job.id} completed successfully")

            # Step 6: Send email notification
            self.email_service.send_job_completion_email(job.email, job, competitors)

        except Exception as e:
            print(f"❌ Job processing failed: {e}")
            raise e

    def _collect_social_posts(
        self,
        competitor: models.Competitor,
        competitor_data: Dict[str, Any],
        db: Session
    ) -> None:
        """
        Collect social media posts for a competitor.

        Args:
            competitor: Competitor database instance
            competitor_data: Raw competitor data
            db: Database session
        """
        try:
            print(f"📱 Starting social media collection for {competitor.name}")

            # Collect posts from each platform using the new collectors
            posts_data = {}

            # LinkedIn posts
            if competitor_data.get("linkedin"):
                try:
                    linkedin_posts = self.linkedin_collector.run(competitor_data["linkedin"])
                    if linkedin_posts:
                        posts_data["linkedin"] = linkedin_posts
                        print(f"  ✅ LinkedIn: {len(linkedin_posts)} posts")
                except Exception as e:
                    print(f"  ❌ LinkedIn collection failed: {e}")

            # Instagram posts
            if competitor_data.get("instagram"):
                try:
                    instagram_posts = self.instagram_collector.run(competitor_data["instagram"], count=10)
                    if instagram_posts:
                        posts_data["instagram"] = instagram_posts
                        print(f"  ✅ Instagram: {len(instagram_posts)} posts")
                except Exception as e:
                    print(f"  ❌ Instagram collection failed: {e}")

            # TikTok posts
            if competitor_data.get("tiktok"):
                try:
                    tiktok_posts = self.tiktok_collector.run(competitor_data["tiktok"], count=10)
                    if tiktok_posts:
                        posts_data["tiktok"] = tiktok_posts
                        print(f"  ✅ TikTok: {len(tiktok_posts)} posts")
                except Exception as e:
                    print(f"  ❌ TikTok collection failed: {e}")

            # Store posts in platform-specific database tables
            total_posts = 0

            for platform, posts in posts_data.items():
                if posts and isinstance(posts, list):
                    for post_url in posts:
                        if post_url:  # Skip empty posts
                            # Store in platform-specific table
                            if platform.lower() == 'linkedin':
                                social_post = models.LinkedInPost(
                                    competitor_id=competitor.id,
                                    post_url=str(post_url)
                                )
                            elif platform.lower() == 'instagram':
                                social_post = models.InstagramPost(
                                    competitor_id=competitor.id,
                                    post_url=str(post_url)
                                )
                            elif platform.lower() == 'facebook':
                                social_post = models.FacebookPost(
                                    competitor_id=competitor.id,
                                    post_url=str(post_url)
                                )
                            elif platform.lower() == 'tiktok':
                                social_post = models.TikTokPost(
                                    competitor_id=competitor.id,
                                    post_url=str(post_url)
                                )
                            else:
                                # Fallback to generic social_posts table for unknown platforms
                                social_post = models.SocialPost(
                                    competitor_id=competitor.id,
                                    platform=platform,
                                    post_url=str(post_url)
                                )

                            db.add(social_post)
                            total_posts += 1

            print(f"📱 Collected {total_posts} social posts for {competitor.name}")

        except Exception as e:
            print(f"❌ Error collecting posts for {competitor.name}: {e}")

    def _calculate_total_posts_for_job(self, job_id: int, db: Session) -> int:
        """
        Calculate total posts for a job across all platform-specific tables.

        Args:
            job_id: Job ID
            db: Database session

        Returns:
            Total number of posts across all platforms
        """
        # Count posts from all platform-specific tables
        linkedin_count = db.query(models.LinkedInPost).join(
            models.Competitor
        ).filter(models.Competitor.job_id == job_id).count()

        instagram_count = db.query(models.InstagramPost).join(
            models.Competitor
        ).filter(models.Competitor.job_id == job_id).count()

        facebook_count = db.query(models.FacebookPost).join(
            models.Competitor
        ).filter(models.Competitor.job_id == job_id).count()

        tiktok_count = db.query(models.TikTokPost).join(
            models.Competitor
        ).filter(models.Competitor.job_id == job_id).count()

        # Legacy social_posts table count
        legacy_count = db.query(models.SocialPost).join(
            models.Competitor
        ).filter(models.Competitor.job_id == job_id).count()

        return linkedin_count + instagram_count + facebook_count + tiktok_count + legacy_count

    def get_job_progress(self, job_id: int) -> Dict[str, Any]:
        """
        Get detailed progress information for a job.

        Args:
            job_id: Job ID

        Returns:
            Dict containing progress information
        """
        db = SessionLocal()
        try:
            job = db.query(models.Job).filter(models.Job.id == job_id).first()
            if not job:
                return {"error": "Job not found"}

            # Count related data
            competitors_count = db.query(models.Competitor).filter(
                models.Competitor.job_id == job_id
            ).count()

            # Count posts from all platform-specific tables
            linkedin_posts_count = db.query(models.LinkedInPost).join(
                models.Competitor
            ).filter(models.Competitor.job_id == job_id).count()

            instagram_posts_count = db.query(models.InstagramPost).join(
                models.Competitor
            ).filter(models.Competitor.job_id == job_id).count()

            facebook_posts_count = db.query(models.FacebookPost).join(
                models.Competitor
            ).filter(models.Competitor.job_id == job_id).count()

            tiktok_posts_count = db.query(models.TikTokPost).join(
                models.Competitor
            ).filter(models.Competitor.job_id == job_id).count()

            # Legacy social_posts table count
            social_posts_count = db.query(models.SocialPost).join(
                models.Competitor
            ).filter(models.Competitor.job_id == job_id).count()

            # Total posts across all platforms
            total_social_posts = (
                linkedin_posts_count + instagram_posts_count +
                facebook_posts_count + tiktok_posts_count + social_posts_count
            )

            # Calculate progress percentage
            progress_percentage = self._calculate_progress_percentage(job.status)

            return {
                "job_id": job_id,
                "status": job.status.value,
                "progress_percentage": progress_percentage,
                "competitors_found": competitors_count,
                "social_posts_collected": total_social_posts,
                "platform_breakdown": {
                    "linkedin": linkedin_posts_count,
                    "instagram": instagram_posts_count,
                    "facebook": facebook_posts_count,
                    "tiktok": tiktok_posts_count,
                    "legacy": social_posts_count
                },
                "created_at": job.created_at,
                "updated_at": job.updated_at,
                "error_message": job.error_message
            }

        except Exception as e:
            return {"error": f"Failed to get job progress: {str(e)}"}
        finally:
            db.close()

    def _calculate_progress_percentage(self, status: models.JobStatus) -> int:
        """
        Calculate progress percentage based on job status.

        Args:
            status: Job status

        Returns:
            Progress percentage (0-100)
        """
        status_progress = {
            models.JobStatus.PENDING: 0,
            models.JobStatus.PROCESSING: 50,
            models.JobStatus.COMPLETED: 100,
            models.JobStatus.FAILED: 0
        }
        return status_progress.get(status, 0)

    def cancel_job(self, job_id: int) -> Dict[str, Any]:
        """
        Cancel a running job.

        Args:
            job_id: Job ID to cancel

        Returns:
            Dict containing cancellation result
        """
        db = SessionLocal()
        try:
            job = db.query(models.Job).filter(models.Job.id == job_id).first()
            if not job:
                return {"error": "Job not found", "success": False}

            if job.status in [models.JobStatus.COMPLETED, models.JobStatus.FAILED]:
                return {
                    "error": f"Cannot cancel job in {job.status.value} status",
                    "success": False
                }

            # Update job status
            job.status = models.JobStatus.FAILED
            job.error_message = "Job cancelled by user"
            job.updated_at = datetime.now(timezone.utc)
            db.commit()

            return {
                "message": f"Job {job_id} cancelled successfully",
                "success": True
            }

        except Exception as e:
            return {"error": f"Failed to cancel job: {str(e)}", "success": False}
        finally:
            db.close()

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get status of all integrated services.

        Returns:
            Dict containing service status information
        """
        return {
            "job_service": {
                "available": True,
                "capabilities": [
                    "background_processing",
                    "progress_tracking",
                    "job_cancellation",
                    "service_integration"
                ]
            },
            "integrated_services": {
                "email_service": self.email_service.is_configured,
                "website_service": self.website_service.is_service_available(),
                "competitor_service": self.competitor_service.is_service_available(),
                "social_media_collectors": {
                    "instagram": True,
                    "linkedin": True,
                    "tiktok": True
                }
            }
        }
