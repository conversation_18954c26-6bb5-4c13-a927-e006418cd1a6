from os import name
from apify_client import ApifyClient
import json
import os
from dotenv import load_dotenv

def scrape_instagram_post(url: str, api_token: str, limit: int = 10) -> dict:
    """
    Scrape Instagram post data using Apify actor

    Args:
        url: Instagram post URL to scrape
        api_token: Apify API token
        limit: Maximum number of results to return

    Returns:
        dict: Scraped data
    """
    # Initialize client
    client = ApifyClient(api_token)

# Configure input,
    run_input = {
        "directUrls": [url],
        "resultsLimit": limit
    }

    try:
        # Run actor and get results
        run = client.actor("SbK00X0JYCPblD2wp").call(run_input=run_input)

# Collect results,
        results = []
        for item in client.dataset(run["defaultDatasetId"]).iterate_items():
            results.append(item)

        return {
            "status": "success",
            "data": results
        }

    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

# Example usage,
if __name__ == "__main__":
    API_TOKEN = "**********************************************"
    POST_URL = "https://www.instagram.com/p/DJtyFcRM-Xn/"
    print("hello")
    result = scrape_instagram_post(POST_URL, API_TOKEN)
    print(result)