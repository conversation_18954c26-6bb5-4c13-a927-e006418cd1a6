# 🚀 Unified Competitor Analysis API

## Overview

**One endpoint to rule them all!** 

The Unified Competitor Analysis API provides a single, powerful endpoint that takes just a competitor website URL and returns comprehensive social media intelligence including posts from all platforms and Instagram comments.

## 🎯 What It Does

**Input**: Just a competitor website URL (e.g., `https://johndeere.com`)

**Output**: Complete competitor social media analysis including:
- ✅ **Social media links** extracted from the website
- ✅ **Posts collected** from ALL platforms (LinkedIn, Instagram, TikTok, Facebook)
- ✅ **Instagram comments** scraped from all Instagram posts
- ✅ **Comprehensive analytics** and engagement insights
- ✅ **Database storage** (optional)

## 🔥 Key Features

### 🔍 **Automatic Discovery**
- Extracts social media links from competitor websites
- No need to manually find Instagram, LinkedIn, TikTok profiles

### 📱 **Multi-Platform Collection**
- **LinkedIn**: Company posts and updates
- **Instagram**: Recent posts + comment scraping
- **TikTok**: Video posts and engagement
- **Facebook**: Posts (when available)

### 💬 **Instagram Comment Intelligence**
- Scrapes comments from Instagram posts using Apify
- Includes usernames, full names, profile URLs, likes, timestamps
- Provides engagement analytics and insights

### 📊 **Comprehensive Analytics**
- Platform-by-platform breakdown
- Instagram engagement metrics
- Success rates and performance data
- Comment analysis and top commenters

## 🚀 API Endpoint

### **POST** `/social-posts/competitor/analyze`

**The only endpoint you need for complete competitor analysis!**

## 📝 Request Schema

```json
{
  "website_url": "https://johndeere.com",
  "post_count": 10,
  "comment_limit": 15,
  "save_to_db": false
}
```

### Parameters:
- **`website_url`** (required): Competitor website URL
- **`post_count`** (optional, default: 10): Posts to collect per platform
- **`comment_limit`** (optional, default: 15): Comments to scrape per Instagram post
- **`save_to_db`** (optional, default: false): Save results to database

## 📊 Response Schema

```json
{
  "status": "success",
  "website_url": "https://johndeere.com",
  "social_media_links": {
    "instagram": "https://instagram.com/johndeere",
    "linkedin": "https://linkedin.com/company/john-deere",
    "tiktok": "https://tiktok.com/@johndeere"
  },
  "posts": {
    "instagram": {
      "platform_url": "https://instagram.com/johndeere",
      "posts": ["https://instagram.com/p/ABC123/", "..."],
      "posts_count": 10,
      "status": "success"
    },
    "linkedin": {
      "platform_url": "https://linkedin.com/company/john-deere",
      "posts": ["https://linkedin.com/posts/...", "..."],
      "posts_count": 8,
      "status": "success"
    },
    "tiktok": {
      "platform_url": "https://tiktok.com/@johndeere",
      "posts": ["https://tiktok.com/@johndeere/video/...", "..."],
      "posts_count": 5,
      "status": "success"
    }
  },
  "instagram_comments": {
    "https://instagram.com/p/ABC123/": {
      "comments": [
        {
          "text": "Great post!",
          "username": "user123",
          "full_name": "John Doe",
          "profile_url": "https://...",
          "likes_count": 5,
          "timestamp": "2024-01-01T12:00:00.000Z"
        }
      ],
      "comment_count": 15,
      "status": "success"
    }
  },
  "instagram_analysis": {
    "total_posts_analyzed": 10,
    "total_comments_collected": 150,
    "successful_scrapes": 8,
    "success_rate": 0.8,
    "average_comments_per_post": 18.75
  },
  "summary": {
    "platforms_analyzed": 3,
    "platforms_with_posts": 3,
    "total_posts_collected": 23,
    "total_instagram_comments": 150,
    "instagram_posts_with_comments": 8,
    "platform_breakdown": {
      "instagram": 10,
      "linkedin": 8,
      "tiktok": 5
    }
  },
  "analysis_timestamp": "2024-01-01T12:00:00.000000"
}
```

## 🛠️ Usage Examples

### cURL
```bash
curl -X POST "http://localhost:8000/social-posts/competitor/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "website_url": "https://johndeere.com",
    "post_count": 10,
    "comment_limit": 15
  }'
```

### Python
```python
import requests

response = requests.post(
    "http://localhost:8000/social-posts/competitor/analyze",
    json={
        "website_url": "https://johndeere.com",
        "post_count": 10,
        "comment_limit": 15,
        "save_to_db": False
    }
)

data = response.json()
if data["status"] == "success":
    print(f"Found {len(data['social_media_links'])} social media platforms")
    print(f"Collected {data['summary']['total_posts_collected']} posts")
    print(f"Scraped {data['summary']['total_instagram_comments']} Instagram comments")
```

### JavaScript
```javascript
const response = await fetch('/social-posts/competitor/analyze', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    website_url: 'https://johndeere.com',
    post_count: 10,
    comment_limit: 15
  })
});

const data = await response.json();
if (data.status === 'success') {
  console.log(`Platforms found: ${Object.keys(data.social_media_links).length}`);
  console.log(`Total posts: ${data.summary.total_posts_collected}`);
  console.log(`Instagram comments: ${data.summary.total_instagram_comments}`);
}
```

## 🔧 Setup & Configuration

### 1. Environment Variables
```bash
# Instagram API (RapidAPI)
INSTAGRAM_RAPID_API_KEY=your_rapidapi_key

# Apify for comment scraping
APIFY_API_TOKEN=your_apify_token

# Database
DATABASE_URL=postgresql://user:password@localhost/dbname
```

### 2. Install Dependencies
```bash
cd lead_processing_api
pip install -r requirements.txt
```

### 3. Run Database Migration
```bash
alembic upgrade head
```

### 4. Start the API
```bash
uvicorn app.main:app --reload
```

## 🧪 Testing

### Run the Test Suite
```bash
cd lead_processing_api
python test_unified_competitor.py
```

### Manual Testing
- **Swagger UI**: `http://localhost:8000/docs`
- **API Health**: `http://localhost:8000/health`
- **Endpoint**: `POST http://localhost:8000/social-posts/competitor/analyze`

## 📈 Performance & Limits

### Typical Processing Times:
- **Social media extraction**: 5-10 seconds
- **Post collection**: 10-30 seconds per platform
- **Instagram comment scraping**: 30-60 seconds per post
- **Total analysis**: 2-5 minutes (depending on post_count and comment_limit)

### Recommended Settings:
- **Development**: `post_count=3, comment_limit=5`
- **Production**: `post_count=10, comment_limit=15`
- **Deep analysis**: `post_count=20, comment_limit=30`

### Rate Limits:
- **RapidAPI**: Check your plan limits
- **Apify**: Monitor usage and costs
- **API**: No built-in limits (consider adding for production)

## 🔒 Security & Best Practices

### API Keys:
- Store in environment variables
- Rotate keys regularly
- Monitor usage and costs

### Database:
- Use connection pooling
- Regular backups
- Monitor storage growth

### Production Deployment:
- Add rate limiting
- Implement caching
- Use background job processing for large analyses
- Monitor performance and errors

## 🆘 Troubleshooting

### Common Issues:

1. **"No social media links found"**
   - Website might not have social media links
   - Check if website is accessible
   - Verify website URL format

2. **"Instagram service not available"**
   - Check APIFY_API_TOKEN environment variable
   - Verify apify-client is installed
   - Test network connectivity

3. **Comment scraping fails**
   - Instagram posts might be private
   - Rate limits might be exceeded
   - Check Apify account status

4. **Slow performance**
   - Reduce post_count and comment_limit
   - Check network connectivity
   - Monitor API rate limits

### Debug Steps:
1. Check API logs for detailed errors
2. Test individual components
3. Verify environment variables
4. Run the test suite

## 🎯 Use Cases

### 🏢 **Business Intelligence**
- Monitor competitor social media activity
- Track engagement trends
- Analyze content strategies

### 📊 **Market Research**
- Discover competitor social presence
- Analyze audience engagement
- Benchmark performance

### 🎨 **Content Strategy**
- Identify successful content types
- Monitor competitor campaigns
- Track engagement patterns

### 🔍 **Lead Generation**
- Find engaged audiences
- Identify potential customers
- Analyze competitor followers

## 🚀 Future Enhancements

- **Facebook integration** (when API access available)
- **Twitter/X integration**
- **Sentiment analysis** of comments
- **Real-time monitoring** and alerts
- **Advanced analytics** and reporting
- **Competitor comparison** features

## 📞 Support

For issues with the unified competitor analysis:
1. Run the test script: `python test_unified_competitor.py`
2. Check Swagger UI: `http://localhost:8000/docs`
3. Review API logs for error details
4. Verify all environment variables are set

The unified endpoint provides the most comprehensive competitor analysis available, combining multiple data sources into a single, powerful API call! 🚀
