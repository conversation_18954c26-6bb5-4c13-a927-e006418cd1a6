"""add instagram comments table

Revision ID: add_instagram_comments
Revises: ecb1c97ee9c7
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_instagram_comments'
down_revision = 'ecb1c97ee9c7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new columns to instagram_posts table
    op.add_column('instagram_posts', sa.Column('comments_scraped', sa.<PERSON>(), nullable=True, default=False))
    op.add_column('instagram_posts', sa.Column('scraped_comments_count', sa.Integer(), nullable=True))
    
    # Create instagram_comments table
    op.create_table('instagram_comments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('post_id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=True),
    sa.Column('profile_url', sa.String(length=1000), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.Column('timestamp', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.ForeignKeyConstraint(['post_id'], ['instagram_posts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_instagram_comments_id'), 'instagram_comments', ['id'], unique=False)


def downgrade() -> None:
    # Drop instagram_comments table
    op.drop_index(op.f('ix_instagram_comments_id'), table_name='instagram_comments')
    op.drop_table('instagram_comments')
    
    # Remove columns from instagram_posts table
    op.drop_column('instagram_posts', 'scraped_comments_count')
    op.drop_column('instagram_posts', 'comments_scraped')
