#!/usr/bin/env python3
"""
Test script for Website Analyzer

This script tests the website analyzer to identify why it's returning generic responses.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Load environment variables
load_dotenv()

def test_openai_connection():
    """Test if OpenAI API is working."""
    print("🔍 Testing OpenAI Connection...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment variables")
        return False
    
    print(f"✅ OpenAI API Key found: {api_key[:10]}...{api_key[-4:]}")
    
    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        
        # Test simple API call
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": "Say 'Hello, API is working!'"}],
            temperature=0.3
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI API Response: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API Error: {e}")
        return False

def test_website_scraping(url="https://dalensai.com"):
    """Test website scraping functionality."""
    print(f"🔍 Testing Website Scraping for: {url}")
    
    try:
        import requests
        from bs4 import BeautifulSoup
        
        # Test basic request
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"✅ HTTP Status: {response.status_code}")
        print(f"✅ Content Length: {len(response.content)} bytes")
        
        # Parse content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove scripts and styles
        for script in soup(["script", "style"]):
            script.decompose()
        
        text = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        clean_text = ' '.join(chunk for chunk in chunks if chunk)
        
        print(f"✅ Extracted Text Length: {len(clean_text)} characters")
        print(f"📄 First 500 characters:")
        print("-" * 50)
        print(clean_text[:500])
        print("-" * 50)
        
        return clean_text
        
    except Exception as e:
        print(f"❌ Website Scraping Error: {e}")
        return None

def test_openai_analysis(website_text, company_name="Dalensai"):
    """Test OpenAI analysis with actual website content."""
    print("🔍 Testing OpenAI Analysis...")
    
    if not website_text:
        print("❌ No website text to analyze")
        return None
    
    try:
        from openai import OpenAI
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        # Limit text length for API
        if len(website_text) > 8000:
            website_text = website_text[:8000] + "..."
        
        prompt = f"""
        Analyze the following business website content and extract structured information:

        Company: {company_name}
        
        Website Content: {website_text}

        Please provide a detailed analysis in the following JSON format:
        {{
            "business_goals": ["goal1", "goal2", "goal3"],
            "about_the_business": ["description1", "description2"],
            "target_market": ["market1", "market2"],
            "products_services": ["product1", "service1"],
            "unique_value_proposition": ["proposition1", "proposition2"]
        }}

        Focus on extracting specific, actionable information about the business.
        """
        
        print(f"📤 Sending prompt to OpenAI (length: {len(prompt)} chars)")
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3
        )
        
        content = response.choices[0].message.content
        print(f"📥 OpenAI Response Length: {len(content)} characters")
        print("📄 Raw OpenAI Response:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        # Try to extract JSON
        try:
            start = content.find('{')
            end = content.rfind('}') + 1
            if start != -1 and end != 0:
                json_str = content[start:end]
                analysis = json.loads(json_str)
                print("✅ JSON Parsing Successful!")
                print("📊 Parsed Analysis:")
                print(json.dumps(analysis, indent=2))
                return analysis
            else:
                print("❌ No JSON found in OpenAI response")
                return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON Parsing Error: {e}")
            print(f"🔍 Attempted to parse: {json_str[:200]}...")
            return None
            
    except Exception as e:
        print(f"❌ OpenAI Analysis Error: {e}")
        return None

def test_full_website_service(url="https://dalensai.com"):
    """Test the full website service."""
    print(f"🔍 Testing Full Website Service for: {url}")
    
    try:
        from app.services.website_service import WebsiteService
        
        website_service = WebsiteService()
        result = website_service.run(url)
        
        print("📊 Website Service Result:")
        print(json.dumps(result, indent=2))
        
        # Check if we got real data or fallback data
        business_goals = result.get("business_goals", [])
        if business_goals and "Business goals for" in business_goals[0]:
            print("❌ Got fallback/generic response")
            return False
        else:
            print("✅ Got real analysis data")
            return True
            
    except Exception as e:
        print(f"❌ Website Service Error: {e}")
        return False

def main():
    """Run all diagnostic tests."""
    print("🧪 Website Analyzer Diagnostic Suite")
    print("=" * 60)
    
    # Test 1: OpenAI Connection
    openai_ok = test_openai_connection()
    
    # Test 2: Website Scraping
    print("\n" + "=" * 60)
    website_text = test_website_scraping()
    
    # Test 3: OpenAI Analysis (only if previous tests passed)
    print("\n" + "=" * 60)
    if openai_ok and website_text:
        analysis = test_openai_analysis(website_text)
    else:
        print("⏭️  Skipping OpenAI analysis (prerequisites failed)")
        analysis = None
    
    # Test 4: Full Website Service
    print("\n" + "=" * 60)
    service_ok = test_full_website_service()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Diagnostic Summary:")
    print(f"  OpenAI Connection: {'✅ OK' if openai_ok else '❌ FAILED'}")
    print(f"  Website Scraping: {'✅ OK' if website_text else '❌ FAILED'}")
    print(f"  OpenAI Analysis: {'✅ OK' if analysis else '❌ FAILED'}")
    print(f"  Website Service: {'✅ OK' if service_ok else '❌ FAILED'}")
    
    if not openai_ok:
        print("\n🔧 Fix: Set OPENAI_API_KEY in your .env file")
    elif not website_text:
        print("\n🔧 Fix: Check internet connection and website accessibility")
    elif not analysis:
        print("\n🔧 Fix: OpenAI API issue or response format problem")
    elif not service_ok:
        print("\n🔧 Fix: Website service implementation issue")
    else:
        print("\n🎉 All tests passed! Website analyzer should be working.")

if __name__ == "__main__":
    main()
