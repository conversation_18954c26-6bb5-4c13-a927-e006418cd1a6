"""Create platform-specific social media tables

Revision ID: ecb1c97ee9c7
Revises: 15c6efc949da
Create Date: 2025-05-29 14:14:43.991630

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ecb1c97ee9c7'
down_revision = '15c6efc949da'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create platform-specific tables
    op.create_table('facebook_posts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=False),
    sa.Column('post_url', sa.String(length=1000), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.<PERSON>umn('comments_count', sa.Integer(), nullable=True),
    sa.Column('shares_count', sa.Integer(), nullable=True),
    sa.Column('reactions_count', sa.Integer(), nullable=True),
    sa.Column('post_type', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_facebook_posts_id'), 'facebook_posts', ['id'], unique=False)

    op.create_table('instagram_posts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=False),
    sa.Column('post_url', sa.String(length=1000), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.Column('comments_count', sa.Integer(), nullable=True),
    sa.Column('media_type', sa.String(length=50), nullable=True),
    sa.Column('hashtags', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_instagram_posts_id'), 'instagram_posts', ['id'], unique=False)

    op.create_table('linkedin_posts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=False),
    sa.Column('post_url', sa.String(length=1000), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.Column('comments_count', sa.Integer(), nullable=True),
    sa.Column('shares_count', sa.Integer(), nullable=True),
    sa.Column('post_type', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_linkedin_posts_id'), 'linkedin_posts', ['id'], unique=False)

    op.create_table('tiktok_posts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('competitor_id', sa.Integer(), nullable=False),
    sa.Column('post_url', sa.String(length=1000), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.Column('comments_count', sa.Integer(), nullable=True),
    sa.Column('shares_count', sa.Integer(), nullable=True),
    sa.Column('views_count', sa.Integer(), nullable=True),
    sa.Column('hashtags', sa.Text(), nullable=True),
    sa.Column('music_title', sa.String(length=255), nullable=True),
    sa.Column('video_duration', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['competitor_id'], ['competitors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tiktok_posts_id'), 'tiktok_posts', ['id'], unique=False)

    # Migrate existing data from social_posts to platform-specific tables
    from sqlalchemy import text
    connection = op.get_bind()

    # Check if social_posts table exists and has data
    result = connection.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'social_posts'"))
    table_exists = result.fetchone()[0] > 0

    if table_exists:
        # Get existing social posts
        result = connection.execute(text("SELECT competitor_id, platform, post_url, content, created_at FROM social_posts"))
        posts = result.fetchall()

        # Migrate posts to appropriate platform tables
        for post in posts:
            competitor_id, platform, post_url, content, created_at = post

            if platform == 'linkedin':
                connection.execute(text("""
                    INSERT INTO linkedin_posts (competitor_id, post_url, content, created_at)
                    VALUES (:competitor_id, :post_url, :content, :created_at)
                """), {
                    'competitor_id': competitor_id,
                    'post_url': post_url,
                    'content': content,
                    'created_at': created_at
                })
            elif platform == 'instagram':
                connection.execute(text("""
                    INSERT INTO instagram_posts (competitor_id, post_url, content, created_at)
                    VALUES (:competitor_id, :post_url, :content, :created_at)
                """), {
                    'competitor_id': competitor_id,
                    'post_url': post_url,
                    'content': content,
                    'created_at': created_at
                })
            elif platform == 'facebook':
                connection.execute(text("""
                    INSERT INTO facebook_posts (competitor_id, post_url, content, created_at)
                    VALUES (:competitor_id, :post_url, :content, :created_at)
                """), {
                    'competitor_id': competitor_id,
                    'post_url': post_url,
                    'content': content,
                    'created_at': created_at
                })
            elif platform == 'tiktok':
                connection.execute(text("""
                    INSERT INTO tiktok_posts (competitor_id, post_url, content, created_at)
                    VALUES (:competitor_id, :post_url, :content, :created_at)
                """), {
                    'competitor_id': competitor_id,
                    'post_url': post_url,
                    'content': content,
                    'created_at': created_at
                })

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tiktok_posts_id'), table_name='tiktok_posts')
    op.drop_table('tiktok_posts')
    op.drop_index(op.f('ix_linkedin_posts_id'), table_name='linkedin_posts')
    op.drop_table('linkedin_posts')
    op.drop_index(op.f('ix_instagram_posts_id'), table_name='instagram_posts')
    op.drop_table('instagram_posts')
    op.drop_index(op.f('ix_facebook_posts_id'), table_name='facebook_posts')
    op.drop_table('facebook_posts')
    # ### end Alembic commands ###
