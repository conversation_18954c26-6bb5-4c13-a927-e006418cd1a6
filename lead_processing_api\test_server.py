#!/usr/bin/env python3
"""
Test script to check if FastAPI server can start properly
"""

import sys
import os

def test_imports():
    """Test if all imports work correctly."""
    print("🔍 Testing imports...")
    
    try:
        from app.main import app
        print("✅ Main app imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def test_app_creation():
    """Test if FastAPI app is created correctly."""
    print("🔍 Testing app creation...")
    
    try:
        from app.main import app
        
        # Check if app is FastAPI instance
        from fastapi import FastAPI
        if isinstance(app, FastAPI):
            print("✅ FastAPI app created successfully")
            print(f"📋 App title: {app.title}")
            print(f"📋 App version: {app.version}")
            return True
        else:
            print("❌ App is not a FastAPI instance")
            return False
            
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def test_routes():
    """Test if routes are properly included."""
    print("🔍 Testing routes...")
    
    try:
        from app.main import app
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print(f"📋 Found {len(routes)} routes:")
        for route in sorted(routes):
            print(f"   {route}")
        
        # Check for essential routes
        essential_routes = ["/", "/docs", "/openapi.json"]
        missing_routes = [route for route in essential_routes if route not in routes]
        
        if missing_routes:
            print(f"❌ Missing essential routes: {missing_routes}")
            return False
        else:
            print("✅ All essential routes found")
            return True
            
    except Exception as e:
        print(f"❌ Routes test error: {e}")
        return False

def test_database():
    """Test database connection."""
    print("🔍 Testing database connection...")
    
    try:
        from app.database import engine
        
        # Try to connect
        with engine.connect() as connection:
            print("✅ Database connection successful")
            return True
            
    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        print("   (This might be okay if database isn't set up yet)")
        return True  # Don't fail the test for database issues

def main():
    """Run all tests."""
    print("🧪 FastAPI Server Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("App Creation Test", test_app_creation),
        ("Routes Test", test_routes),
        ("Database Test", test_database)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Try starting the server:")
        print("   uvicorn app.main:app --reload")
        print("   Then visit: http://127.0.0.1:8000/docs")
    else:
        print("\n⚠️  Some tests failed. Fix the issues above before starting the server.")

if __name__ == "__main__":
    main()
