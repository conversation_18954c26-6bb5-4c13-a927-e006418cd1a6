"""
Unified Competitor Analysis Service

This service provides a single endpoint that:
1. Takes a competitor website URL
2. Extracts social media links from the website
3. Collects posts from all social media platforms
4. Scrapes comments from Instagram posts
5. Returns comprehensive analysis in one response
"""

import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

# Add the lead_processing directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))

try:
    from post_collector import Instagram, LinkedInCollector, Tiktok
    import website as website_module
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Unified competitor service: Could not import modules: {e}")
    SERVICES_AVAILABLE = False
    Instagram = None
    LinkedInCollector = None
    Tiktok = None
    website_module = None

from ..models import Competitor, InstagramPost, InstagramComment


class UnifiedCompetitorService:
    """
    Unified service for comprehensive competitor analysis.
    """

    def __init__(self):
        """Initialize the unified competitor service."""
        self.is_available = SERVICES_AVAILABLE
        if self.is_available:
            self.instagram = Instagram()
            self.linkedin = LinkedInCollector()
            self.tiktok = Tiktok()

    def analyze_competitor_comprehensive(
        self,
        website_url: str,
        post_count: int = 10,
        comment_limit: int = 15,
        save_to_db: bool = False,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive competitor analysis from website URL.

        Args:
            website_url: Competitor website URL
            post_count: Number of posts to collect per platform
            comment_limit: Number of comments to scrape per Instagram post
            save_to_db: Whether to save results to database
            db: Database session

        Returns:
            Dict containing comprehensive analysis with posts and Instagram comments
        """
        if not self.is_available:
            return {
                "status": "error",
                "message": "Unified competitor service not available",
                "website_url": website_url,
                "social_media_links": {},
                "posts": {},
                "instagram_comments": {},
                "summary": self._empty_summary(),
                "analysis_timestamp": datetime.now().isoformat()
            }

        try:
            print(f"🔍 Starting comprehensive analysis for: {website_url}")

            # Step 1: Extract social media links from website
            print("📱 Extracting social media links...")
            social_links = self._extract_social_media_links(website_url)

            if not social_links:
                return {
                    "status": "error",
                    "message": "No social media links found on website",
                    "website_url": website_url,
                    "social_media_links": {},
                    "posts": {},
                    "instagram_comments": {},
                    "summary": self._empty_summary(),
                    "analysis_timestamp": datetime.now().isoformat()
                }

            print(f"✅ Found social media links: {list(social_links.keys())}")

            # Step 2: Collect posts from all platforms
            print("📊 Collecting posts from all platforms...")
            all_posts = self._collect_all_platform_posts(social_links, post_count)

            # Step 3: Scrape Instagram comments
            instagram_comments = {}
            instagram_analysis = {}

            if "instagram" in social_links and all_posts.get("instagram", {}).get("posts"):
                print("💬 Scraping Instagram comments...")
                instagram_comments, instagram_analysis = self._scrape_instagram_comments(
                    all_posts["instagram"]["posts"],
                    comment_limit
                )

            # Step 4: Generate comprehensive summary
            summary = self._generate_summary(all_posts, instagram_comments)

            # Step 5: Save to database if requested
            if save_to_db and db:
                competitor_id = self._save_to_database(
                    website_url=website_url,
                    social_links=social_links,
                    posts=all_posts,
                    instagram_comments=instagram_comments,
                    db=db
                )
                summary["competitor_id"] = competitor_id

            print("✅ Comprehensive analysis completed!")

            return {
                "status": "success",
                "website_url": website_url,
                "social_media_links": social_links,
                "posts": all_posts,
                "instagram_comments": instagram_comments,
                "instagram_analysis": instagram_analysis,
                "summary": summary,
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ Comprehensive analysis failed: {str(e)}")
            return {
                "status": "error",
                "message": f"Analysis failed: {str(e)}",
                "website_url": website_url,
                "social_media_links": {},
                "posts": {},
                "instagram_comments": {},
                "summary": self._empty_summary(),
                "analysis_timestamp": datetime.now().isoformat()
            }

    def _extract_social_media_links(self, website_url: str) -> Dict[str, str]:
        """Extract social media links from website."""
        try:
            # Use the website module to extract social media links
            result = website_module.extract_business_info(website_url)

            if isinstance(result, dict) and "social_media" in result:
                social_links = result["social_media"]

                # Clean up the links - remove empty or "N/A" values
                cleaned_links = {}
                for platform, url in social_links.items():
                    if url and url != "N/A" and url.strip():
                        cleaned_links[platform] = url.strip()

                return cleaned_links

            return {}

        except Exception as e:
            print(f"⚠️  Error extracting social media links: {str(e)}")
            return {}

    def _collect_all_platform_posts(self, social_links: Dict[str, str], post_count: int) -> Dict[str, Any]:
        """Collect posts from all available social media platforms."""
        all_posts = {}

        for platform, url in social_links.items():
            print(f"📱 Collecting {platform} posts from: {url}")

            try:
                if platform.lower() == "linkedin":
                    posts = self.linkedin.run(url)
                elif platform.lower() == "instagram":
                    posts = self.instagram.run(url, count=post_count)
                elif platform.lower() == "tiktok":
                    posts = self.tiktok.run(url, count=post_count)
                elif platform.lower() == "facebook":
                    # Facebook not implemented yet
                    posts = []
                else:
                    posts = []

                # Ensure posts is a list
                if not isinstance(posts, list):
                    posts = []

                all_posts[platform] = {
                    "platform_url": url,
                    "posts": posts,
                    "posts_count": len(posts),
                    "status": "success" if posts else "no_posts_found"
                }

                print(f"✅ {platform}: Found {len(posts)} posts")

            except Exception as e:
                print(f"❌ {platform} collection failed: {str(e)}")
                all_posts[platform] = {
                    "platform_url": url,
                    "posts": [],
                    "posts_count": 0,
                    "status": "error",
                    "error": str(e)
                }

        return all_posts

    def _scrape_instagram_comments(self, instagram_posts: List[str], comment_limit: int) -> tuple:
        """Scrape comments from Instagram posts."""
        instagram_comments = {}
        total_comments = 0
        successful_scrapes = 0

        for i, post_url in enumerate(instagram_posts, 1):
            print(f"💬 Scraping comments from Instagram post {i}/{len(instagram_posts)}")

            try:
                comment_result = self.instagram.scrape_post_comments(
                    url=post_url,
                    limit=comment_limit
                )

                if comment_result.get("status") == "success":
                    comments_data = comment_result.get("data", [])

                    # Convert to simple format
                    comments = []
                    for comment_data in comments_data:
                        comment = {
                            "text": comment_data.get("text", ""),
                            "username": comment_data.get("username", ""),
                            "full_name": comment_data.get("full_name", ""),
                            "profile_url": comment_data.get("profile_url", ""),
                            "likes_count": comment_data.get("likes_count", 0),
                            "timestamp": comment_data.get("timestamp", "")
                        }
                        comments.append(comment)

                    instagram_comments[post_url] = {
                        "comments": comments,
                        "comment_count": len(comments),
                        "status": "success"
                    }

                    total_comments += len(comments)
                    successful_scrapes += 1
                    print(f"✅ Scraped {len(comments)} comments from post {i}")

                else:
                    instagram_comments[post_url] = {
                        "comments": [],
                        "comment_count": 0,
                        "status": "error",
                        "error": comment_result.get("message", "Unknown error")
                    }
                    print(f"❌ Failed to scrape comments from post {i}")

            except Exception as e:
                instagram_comments[post_url] = {
                    "comments": [],
                    "comment_count": 0,
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Error scraping post {i}: {str(e)}")

        # Generate Instagram-specific analysis
        instagram_analysis = {
            "total_posts_analyzed": len(instagram_posts),
            "total_comments_collected": total_comments,
            "successful_scrapes": successful_scrapes,
            "success_rate": successful_scrapes / len(instagram_posts) if instagram_posts else 0,
            "average_comments_per_post": total_comments / successful_scrapes if successful_scrapes > 0 else 0
        }

        return instagram_comments, instagram_analysis

    def _generate_summary(self, all_posts: Dict[str, Any], instagram_comments: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary of the analysis."""
        total_posts = sum(platform_data.get("posts_count", 0) for platform_data in all_posts.values())
        total_instagram_comments = sum(
            post_data.get("comment_count", 0)
            for post_data in instagram_comments.values()
        )

        platforms_analyzed = len(all_posts)
        platforms_with_posts = len([
            platform for platform, data in all_posts.items()
            if data.get("posts_count", 0) > 0
        ])

        return {
            "platforms_analyzed": platforms_analyzed,
            "platforms_with_posts": platforms_with_posts,
            "total_posts_collected": total_posts,
            "total_instagram_comments": total_instagram_comments,
            "instagram_posts_with_comments": len([
                post for post in instagram_comments.values()
                if post.get("comment_count", 0) > 0
            ]),
            "platform_breakdown": {
                platform: data.get("posts_count", 0)
                for platform, data in all_posts.items()
            }
        }

    def _save_to_database(
        self,
        website_url: str,
        social_links: Dict[str, str],
        posts: Dict[str, Any],
        instagram_comments: Dict[str, Any],
        db: Session
    ) -> int:
        """Save analysis results to database."""
        try:
            # Create competitor record
            competitor = Competitor(
                name=website_url.replace("https://", "").replace("http://", "").split("/")[0],
                linkedin=social_links.get("linkedin"),
                instagram=social_links.get("instagram"),
                facebook=social_links.get("facebook"),
                tiktok=social_links.get("tiktok"),
                job_id=1  # You might want to make this configurable
            )
            db.add(competitor)
            db.flush()

            # Save Instagram posts and comments
            if "instagram" in posts and instagram_comments:
                for post_url in posts["instagram"]["posts"]:
                    instagram_post = InstagramPost(
                        competitor_id=competitor.id,
                        post_url=post_url,
                        comments_scraped=post_url in instagram_comments,
                        scraped_comments_count=instagram_comments.get(post_url, {}).get("comment_count", 0)
                    )
                    db.add(instagram_post)
                    db.flush()

                    # Save comments for this post
                    if post_url in instagram_comments:
                        for comment_data in instagram_comments[post_url].get("comments", []):
                            instagram_comment = InstagramComment(
                                post_id=instagram_post.id,
                                competitor_id=competitor.id,
                                text=comment_data["text"],
                                username=comment_data["username"],
                                full_name=comment_data["full_name"],
                                profile_url=comment_data["profile_url"],
                                likes_count=comment_data["likes_count"],
                                timestamp=comment_data["timestamp"]
                            )
                            db.add(instagram_comment)

            db.commit()
            return competitor.id

        except Exception as e:
            db.rollback()
            raise Exception(f"Database save failed: {str(e)}")

    def _empty_summary(self) -> Dict[str, Any]:
        """Return empty summary for error cases."""
        return {
            "platforms_analyzed": 0,
            "platforms_with_posts": 0,
            "total_posts_collected": 0,
            "total_instagram_comments": 0,
            "instagram_posts_with_comments": 0,
            "platform_breakdown": {}
        }
