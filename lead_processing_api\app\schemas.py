from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List, Dict, Any
from .models import JobStatus

# User Schemas

class UserBase(BaseModel):
    """Base schema for user data."""
    username: str
    email: EmailStr

class UserCreate(UserBase):
    """Schema for creating a new user."""
    password: str

class UserResponse(UserBase):
    """Schema for user response (without password)."""
    id: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    """Schema for user login."""
    username: str
    password: str

# Token Schemas

class Token(BaseModel):
    """Schema for JWT token response."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Schema for token data."""
    username: Optional[str] = None

# Job Schemas

class JobBase(BaseModel):
    """Base schema for job data."""
    business_domain: str
    website: str
    goals: Optional[str] = None
    email: EmailStr

class JobCreate(JobBase):
    """Schema for creating a new job."""
    pass

class JobUpdate(BaseModel):
    """Schema for updating job status."""
    status: JobStatus
    error_message: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None

class Job(JobBase):
    """Schema for job response."""
    id: int
    status: JobStatus
    created_at: datetime
    updated_at: datetime
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

# Competitor Schemas

class CompetitorBase(BaseModel):
    """Base schema for competitor data."""
    name: str
    linkedin: Optional[str] = None
    instagram: Optional[str] = None
    facebook: Optional[str] = None
    tiktok: Optional[str] = None

class CompetitorCreate(CompetitorBase):
    """Schema for creating a new competitor."""
    job_id: int

class Competitor(CompetitorBase):
    """Schema for competitor response."""
    id: int
    job_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Social Post Schemas

class SocialPostBase(BaseModel):
    """Base schema for social post data."""
    platform: str
    post_url: str
    content: Optional[str] = None

class SocialPostCreate(SocialPostBase):
    """Schema for creating a new social post."""
    competitor_id: int

class SocialPost(SocialPostBase):
    """Schema for social post response."""
    id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Platform-specific Social Post Schemas

class LinkedInPostBase(BaseModel):
    """Base schema for LinkedIn post data."""
    post_url: str
    content: Optional[str] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None
    post_type: Optional[str] = None

class LinkedInPostCreate(LinkedInPostBase):
    """Schema for creating a new LinkedIn post."""
    competitor_id: int

class LinkedInPost(LinkedInPostBase):
    """Schema for LinkedIn post response."""
    id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class InstagramPostBase(BaseModel):
    """Base schema for Instagram post data."""
    post_url: str
    content: Optional[str] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    media_type: Optional[str] = None
    hashtags: Optional[str] = None
    comments_scraped: Optional[bool] = False
    scraped_comments_count: Optional[int] = None

class InstagramPostCreate(InstagramPostBase):
    """Schema for creating a new Instagram post."""
    competitor_id: int

class InstagramPost(InstagramPostBase):
    """Schema for Instagram post response."""
    id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Instagram Comment Schemas

class InstagramCommentBase(BaseModel):
    """Base schema for Instagram comment data."""
    text: str
    username: str
    full_name: Optional[str] = None
    profile_url: Optional[str] = None
    likes_count: Optional[int] = None
    timestamp: Optional[str] = None

class InstagramCommentCreate(InstagramCommentBase):
    """Schema for creating a new Instagram comment."""
    post_id: int
    competitor_id: int

class InstagramComment(InstagramCommentBase):
    """Schema for Instagram comment response."""
    id: int
    post_id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class FacebookPostBase(BaseModel):
    """Base schema for Facebook post data."""
    post_url: str
    content: Optional[str] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None
    reactions_count: Optional[int] = None
    post_type: Optional[str] = None

class FacebookPostCreate(FacebookPostBase):
    """Schema for creating a new Facebook post."""
    competitor_id: int

class FacebookPost(FacebookPostBase):
    """Schema for Facebook post response."""
    id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class TikTokPostBase(BaseModel):
    """Base schema for TikTok post data."""
    post_url: str
    content: Optional[str] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None
    views_count: Optional[int] = None
    hashtags: Optional[str] = None
    music_title: Optional[str] = None
    video_duration: Optional[int] = None

class TikTokPostCreate(TikTokPostBase):
    """Schema for creating a new TikTok post."""
    competitor_id: int

class TikTokPost(TikTokPostBase):
    """Schema for TikTok post response."""
    id: int
    competitor_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Extended schemas with relationships

class CompetitorWithPosts(Competitor):
    """Competitor schema with social posts included."""
    social_posts: List[SocialPost] = []

    # Platform-specific posts
    linkedin_posts: List[LinkedInPost] = []
    instagram_posts: List[InstagramPost] = []
    facebook_posts: List[FacebookPost] = []
    tiktok_posts: List[TikTokPost] = []

class JobWithDetails(Job):
    """Job schema with competitors and their posts included."""
    competitors: List[CompetitorWithPosts] = []

# Job Status Response Schema

class JobStatusResponse(BaseModel):
    """Schema for job status check response."""
    id: int
    status: JobStatus
    created_at: datetime
    updated_at: datetime
    progress_message: Optional[str] = None
    error_message: Optional[str] = None

# Job Result Schema

class JobResult(BaseModel):
    """Schema for completed job results."""
    id: int
    status: JobStatus
    business_analysis: Optional[Dict[str, Any]] = None
    competitors_found: int
    total_posts_collected: int
    competitors: List[CompetitorWithPosts] = []
    created_at: datetime
    completed_at: datetime

# Instagram Comprehensive Analysis Schemas

class InstagramCommentScrapeRequest(BaseModel):
    """Request schema for scraping comments from a single Instagram post."""
    post_url: str
    limit: Optional[int] = 10
    timeout: Optional[int] = 120

class InstagramCommentScrapeResponse(BaseModel):
    """Response schema for Instagram comment scraping."""
    status: str
    post_url: str
    comments: List[InstagramComment]
    total_comments: int
    message: Optional[str] = None

class InstagramComprehensiveRequest(BaseModel):
    """Request schema for comprehensive Instagram analysis."""
    profile_url: str
    post_count: Optional[int] = 10
    comment_limit: Optional[int] = 10
    save_to_db: Optional[bool] = False
    competitor_id: Optional[int] = None

class InstagramPostWithComments(BaseModel):
    """Schema for Instagram post with its comments."""
    url: str
    position: int
    comments_scraped: bool
    comment_count: int
    comments: List[InstagramComment]

class InstagramAnalysisSummary(BaseModel):
    """Schema for Instagram analysis summary."""
    total_posts: int
    total_comments: int
    posts_with_comments: int
    average_comments_per_post: float
    comment_collection_success_rate: float

class InstagramEngagementAnalysis(BaseModel):
    """Schema for Instagram engagement analysis."""
    total_engagement: int
    average_comments_per_post: float
    most_engaging_posts: List[Dict[str, Any]]

class InstagramContentInsights(BaseModel):
    """Schema for Instagram content insights."""
    total_comments: int
    unique_commenters: int
    engagement_diversity: float
    top_commenters: List[Dict[str, Any]]

class InstagramComprehensiveResponse(BaseModel):
    """Response schema for comprehensive Instagram analysis."""
    status: str
    profile_url: str
    posts: List[InstagramPostWithComments]
    summary: InstagramAnalysisSummary
    engagement_analysis: Optional[InstagramEngagementAnalysis] = None
    content_insights: Optional[InstagramContentInsights] = None
    analysis_timestamp: str
    message: Optional[str] = None

# Unified Competitor Analysis Schemas

class UnifiedCompetitorRequest(BaseModel):
    """Request schema for unified competitor analysis."""
    website_url: str
    post_count: Optional[int] = 10
    comment_limit: Optional[int] = 15
    save_to_db: Optional[bool] = False

class PlatformPostsData(BaseModel):
    """Schema for platform-specific posts data."""
    platform_url: str
    posts: List[str]
    posts_count: int
    status: str
    error: Optional[str] = None

class InstagramCommentsData(BaseModel):
    """Schema for Instagram comments data per post."""
    comments: List[Dict[str, Any]]
    comment_count: int
    status: str
    error: Optional[str] = None

class UnifiedAnalysisSummary(BaseModel):
    """Schema for unified analysis summary."""
    platforms_analyzed: int
    platforms_with_posts: int
    total_posts_collected: int
    total_instagram_comments: int
    instagram_posts_with_comments: int
    platform_breakdown: Dict[str, int]
    competitor_id: Optional[int] = None

class InstagramAnalysisData(BaseModel):
    """Schema for Instagram-specific analysis."""
    total_posts_analyzed: int
    total_comments_collected: int
    successful_scrapes: int
    success_rate: float
    average_comments_per_post: float

class UnifiedCompetitorResponse(BaseModel):
    """Response schema for unified competitor analysis."""
    status: str
    website_url: str
    social_media_links: Dict[str, str]
    posts: Dict[str, PlatformPostsData]
    instagram_comments: Dict[str, InstagramCommentsData]
    instagram_analysis: Optional[InstagramAnalysisData] = None
    summary: UnifiedAnalysisSummary
    analysis_timestamp: str
    message: Optional[str] = None
