import requests
import re
import json
from typing import Dict


class LinkedInCollector:
    """
    A class to collect and process data from LinkedIn posts using RapidAPI.
    
    This class provides functionality to:
    - Extract post IDs from LinkedIn URLs
    - Fetch comments from LinkedIn posts
    - Format and structure the collected data
    
    Attributes:
        rapidapi_key (str): Authentication key for RapidAPI services
        headers (dict): HTTP headers required for API requests
    """

    def __init__(self, rapidapi_key: str):
        """
        Initialize the LinkedIn collector with API credentials.
        
        Args:
            rapidapi_key (str): Your RapidAPI authentication key
        
        Sets up:
            - Request headers with authentication
        """
        self.rapidapi_key = rapidapi_key
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "linkedin-data-api.p.rapidapi.com"
        }

    def extract_post_id(self, post_url: str) -> str:
        """
        Extract the post ID from a LinkedIn URL.

        Args:
            post_url (str): Complete LinkedIn post URL

        Returns:
            str: Extracted post ID

        Raises:
            ValueError: If post ID cannot be extracted from the URL

        Supported URL format:
            - linkedin.com/posts/activity-{id}
        """
        match = re.search(r'activity[-:](\d+)', post_url)
        if not match:
            raise ValueError("Could not extract post ID from URL")
        return match.group(1)

    def get_post_comments(self, post_id: str) -> Dict:
        """
        Fetch comments for a specific LinkedIn post.

        Args:
            post_id (str): The unique identifier of the LinkedIn post

        Returns:
            Dict: JSON response containing post comments and metadata
                 Format: {
                     "data": {
                         "comments": [
                             {
                                 "text": str,
                                 "author": {
                                     "username": str,
                                     "linkedinUrl": str
                                 }
                             },
                             ...
                         ]
                     }
                 }
        """
        url = "https://linkedin-data-api.p.rapidapi.com/get-profile-post-and-comments"
        params = {"urn": post_id}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error fetching comments: {e}")
            return {}

    def run(self, post_url: str) -> str:
        """
        Main execution method to process a LinkedIn post URL and extract formatted comments.

        Args:
            post_url (str): LinkedIn post URL to process

        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "comment": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]

        Raises:
            Exception: If any error occurs during processing
        """
        try:
            post_id = self.extract_post_id(post_url)
            response_data = self.get_post_comments(post_id)
            
            formatted_comments = []
            for comment in response_data.get('data', {}).get('comments', []):
                author = comment.get('author', {})
                formatted_comments.append({
                    "comment": comment.get('text', ''),
                    "commenter_name": author.get('username', ''),
                    "commenter_profile_link": author.get('linkedinUrl', '')
                })

            return json.dumps(formatted_comments, indent=2, ensure_ascii=False)
        except Exception as e:
            return str(e)