from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional

from ..services.competitor_service import CompetitorService

router = APIRouter(
    prefix="/competitors",
    tags=["competitors"],
)

# Pydantic models for competitor analysis
class CompetitorAnalysisRequest(BaseModel):
    """Request model for competitor analysis."""
    url: str
    domain: Optional[str] = None
    business_goal: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "url": "example-farm.com",
                "domain": "agriculture",
                "business_goal": "Promote sustainable organic farming"
            }
        }

class CompetitorSocialMedia(BaseModel):
    """Social media links for a competitor."""
    facebook: str
    instagram: str
    linkedin: str
    tiktok: str

class CompetitorInfo(BaseModel):
    """Individual competitor information."""
    name: str
    linkedin: str
    instagram: str
    facebook: str
    tiktok: str

class BusinessAnalysis(BaseModel):
    """Business analysis information."""
    primary_domain: str
    specific_focus: str
    market_position: str

class CompetitorAnalysisResponse(BaseModel):
    """Response model for competitor analysis."""
    business_analysis: BusinessAnalysis
    competitors: List[CompetitorInfo]
    total_competitors: int

    class Config:
        json_schema_extra = {
            "example": {
                "business_analysis": {
                    "primary_domain": "Agriculture",
                    "specific_focus": "Organic farming and sustainable practices",
                    "market_position": "Premium organic produce market"
                },
                "competitors": [
                    {
                        "name": "Organic Valley",
                        "linkedin": "https://www.linkedin.com/company/organic-valley",
                        "instagram": "https://www.instagram.com/organicvalley/",
                        "facebook": "https://www.facebook.com/OrganicValley",
                        "tiktok": "N/A"
                    }
                ],
                "total_competitors": 5
            }
        }

class BusinessInfoRequest(BaseModel):
    """Request model for extracting business info from URL."""
    url: str

    class Config:
        json_schema_extra = {
            "example": {
                "url": "example-farm.com"
            }
        }

class BusinessInfoResponse(BaseModel):
    """Response model for business info extraction."""
    domain: str
    business_goal: str

    class Config:
        json_schema_extra = {
            "example": {
                "domain": "agriculture",
                "business_goal": "Promote sustainable organic farming practices"
            }
        }

@router.post("/analyze", response_model=CompetitorAnalysisResponse)
def analyze_competitors(request: CompetitorAnalysisRequest):
    """
    Analyze competitors for a given business.

    This endpoint:
    1. Scrapes the business website
    2. Uses AI to identify top competitors
    3. Finds social media links for each competitor
    4. Returns structured competitor data

    **Parameters:**
    - **url**: Business website URL
    - **domain**: Business domain/industry (optional, will be auto-detected)
    - **business_goal**: Business goals/mission (optional, will be auto-detected)

    **Returns:**
    - Business analysis and list of competitors with social media links
    """
    try:
        # Initialize the competitor service
        competitor_service = CompetitorService()

        # Get competitor analysis
        result = competitor_service.analyze_competitors(
            request.url,
            request.domain or "Unknown",
            request.business_goal
        )

        if not result.get("analysis_successful", False):
            raise HTTPException(
                status_code=500,
                detail=result.get("error", "Competitor analysis failed")
            )

        # Create business analysis (simplified since we don't have the detailed analysis structure)
        business_analysis = BusinessAnalysis(
            primary_domain=request.domain or "Unknown",
            specific_focus=request.business_goal or "Unknown",
            market_position="Analysis completed"
        )

        competitors = [
            CompetitorInfo(
                name=comp["name"],
                linkedin=comp["linkedin"],
                instagram=comp["instagram"],
                facebook=comp["facebook"],
                tiktok=comp["tiktok"]
            )
            for comp in result.get("competitors", [])
        ]

        response = CompetitorAnalysisResponse(
            business_analysis=business_analysis,
            competitors=competitors,
            total_competitors=len(competitors)
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Competitor analysis failed: {str(e)}"
        )

@router.post("/extract-business-info", response_model=BusinessInfoResponse)
def extract_business_info(request: BusinessInfoRequest):
    """
    Extract business domain and goals from a website URL.

    This endpoint analyzes a website and automatically determines:
    - Business domain/industry
    - Business goals and mission

    **Parameters:**
    - **url**: Website URL to analyze

    **Returns:**
    - Extracted domain and business goal information
    """
    try:
        # Initialize the competitor service
        competitor_service = CompetitorService()

        # Extract business information
        business_info = competitor_service.extract_business_info(request.url)

        response = BusinessInfoResponse(
            domain=business_info.get("domain", "Unknown"),
            business_goal=business_info.get("business_goal", "Not specified")
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Business info extraction failed: {str(e)}"
        )

@router.get("/health")
def competitors_health_check():
    """Health check for competitor analysis functionality."""
    try:
        # Test if competitor service can be initialized
        competitor_service = CompetitorService()
        return {"status": "available", "message": "Competitor analysis ready"}
    except Exception as e:
        return {"status": "error", "message": f"Competitor service initialization failed: {str(e)}"}
