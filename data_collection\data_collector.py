from instagram import InstagramCollector
from tiktok import <PERSON><PERSON><PERSON>okCollector
from facebook import FacebookDataGatherer
from linkedin import LinkedInCollector
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class SocialMediaDataCollector:
    """
    A unified interface for collecting data from multiple social media platforms.
    
    This class provides a centralized way to collect comments and related data
    from various social media platforms including Instagram, Facebook, TikTok,
    and LinkedIn using their respective APIs through RapidAPI.
    
    Attributes:
        instagram_api_key (str): RapidAPI key for Instagram API access
        facebook_api_key (str): RapidAPI key for Facebook API access
        linkedin_api_key (str): RapidAPI key for LinkedIn API access
        tiktok_api_key (str): RapidAPI key for TikTok API access
    
    Environment Variables Required:
        INSTAGRAM_RAPID_API_KEY: API key for Instagram
        FACEBOOK_RAPID_API_KEY: API key for Facebook
        LINKEDIN_RAPID_API_KEY: API key for LinkedIn
        TIKTOK_RAPID_API_KEY: API key for TikTok
    """

    def __init__(self):
        """
        Initialize the social media data collector with API keys from environment variables.
        
        Loads API keys for all supported platforms from environment variables.
        Each API key should be set in the .env file before instantiating this class.
        """
        self.instagram_api_key = os.getenv('INSTAGRAM_RAPID_API_KEY')
        self.facebook_api_key = os.getenv('FACEBOOK_RAPID_API_KEY')
        self.linkedin_api_key = os.getenv('LINKEDIN_RAPID_API_KEY')
        self.tiktok_api_key = os.getenv('TIKTOK_RAPID_API_KEY')
 
    def instagram(self, url: str) -> str:
        """
        Collect comments from an Instagram post or reel.
        
        Args:
            url (str): Complete URL to an Instagram post or reel
                      Format: https://www.instagram.com/reel/{shortcode}/ or
                             https://www.instagram.com/p/{shortcode}/
        
        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "text": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]
        """
        instagram = InstagramCollector(self.instagram_api_key)
        comments = instagram.run(url)
        return comments

    def facebook(self, url: str) -> str:
        """
        Collect comments from a Facebook post.
        
        Args:
            url (str): Complete URL to a Facebook post
                      Format: https://www.facebook.com/{page}/posts/{id}/ or
                             https://web.facebook.com/groups/{group}/posts/{id}/
        
        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "text": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]
        """
        facebook = FacebookDataGatherer(self.facebook_api_key)
        comments = facebook.run(url)
        return comments

    def tiktok(self, url: str) -> str:
        """
        Collect comments from a TikTok video.
        
        Args:
            url (str): Complete URL to a TikTok video
                      Format: https://www.tiktok.com/@{username}/video/{id}
        
        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "comment": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]
        """
        tiktok = TikTokCollector(self.tiktok_api_key)
        comments = tiktok.run(url)
        return comments

    def linkedin(self, url: str) -> str:
        """
        Collect comments from a LinkedIn post.
        
        Args:
            url (str): Complete URL to a LinkedIn post
                      Format: https://www.linkedin.com/posts/activity-{id}
        
        Returns:
            str: JSON string containing formatted comments with structure:
                [
                    {
                        "comment": str,
                        "commenter_name": str,
                        "commenter_profile_link": str
                    },
                    ...
                ]
        """
        linkedin = LinkedInCollector(self.linkedin_api_key)
        comments = linkedin.run(url)
        return comments


if __name__ == '__main__':
    # Example usage of the SocialMediaDataCollector
    collector = SocialMediaDataCollector()

    # Example LinkedIn post
    linkedin_comment = collector.linkedin(
        url="https://www.linkedin.com/posts/activity-7319464821593100288"
    )
    print("LinkedIn Comments:", linkedin_comment)

    # Example Facebook post
    facebook_comment = collector.facebook(
        url="https://web.facebook.com/groups/parejasfacebook/posts/319967887082956/"
    )
    print("Facebook Comments:", facebook_comment)

    # Example TikTok video
    tiktok_comment = collector.tiktok(
        url="https://www.tiktok.com/@tiktok/video/7093219391759764782"
    )
    print("TikTok Comments:", tiktok_comment)

    # Example Instagram post
    instagram_comment = collector.instagram(
        url="https://www.instagram.com/reel/DGOY-e1Ibbn/?igsh=Z2lxb3d2ZnRkaWls"
    )
    print("Instagram Comments:", instagram_comment)