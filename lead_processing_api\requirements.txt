# FastAPI and web framework dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Database dependencies
sqlalchemy>=2.0.23
psycopg2-binary>=2.9.9
alembic>=1.13.0

# Pydantic and validation
pydantic[email]>=2.5.0

# Authentication dependencies
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Environment and configuration
python-dotenv>=1.0.0

# Email functionality
secure-smtplib>=0.1.1

# Background tasks (optional - can use Celery instead)
celery>=5.3.4
redis>=5.0.1

# Existing lead processing dependencies
openai>=1.0.0
requests>=2.28.1
beautifulsoup4>=4.11.1
rich>=12.6.0
apify-client>=1.10.0

# Development and testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
httpx>=0.25.2
