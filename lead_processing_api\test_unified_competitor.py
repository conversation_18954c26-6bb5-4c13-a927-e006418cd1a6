#!/usr/bin/env python3
"""
Test script for Unified Competitor Analysis API

This script tests the new unified endpoint that:
1. Takes a competitor website URL
2. Extracts social media links
3. Collects posts from all platforms
4. Scrapes Instagram comments
5. Returns comprehensive analysis
"""

import requests
import json
import time
from typing import Dict, Any


class UnifiedCompetitorTester:
    """Test class for unified competitor analysis endpoint"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the tester with API base URL"""
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """Test if the API is running"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ API health check passed")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API health check failed: {str(e)}")
            return False
    
    def test_api_documentation(self) -> bool:
        """Test if the unified endpoint is documented"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                social_media = data.get('available_endpoints', {}).get('social_media', {})
                
                if '🚀 unified_competitor_analysis' in social_media:
                    print("✅ Unified competitor analysis endpoint is documented")
                    print(f"   Endpoint: {social_media['🚀 unified_competitor_analysis']}")
                    return True
                else:
                    print("❌ Unified endpoint not found in documentation")
                    return False
            else:
                print(f"❌ API documentation check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API documentation check failed: {str(e)}")
            return False
    
    def test_unified_competitor_analysis(self, website_url: str = "https://johndeere.com") -> bool:
        """Test the unified competitor analysis endpoint"""
        print(f"\n🧪 Testing unified competitor analysis for: {website_url}")
        
        payload = {
            "website_url": website_url,
            "post_count": 3,  # Small number for testing
            "comment_limit": 5,  # Small number for testing
            "save_to_db": False
        }
        
        try:
            print("📤 Sending request to unified endpoint...")
            response = self.session.post(
                f"{self.base_url}/social-posts/competitor/analyze",
                json=payload,
                timeout=300  # 5 minutes timeout for comprehensive analysis
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Unified analysis: {data['status']}")
                
                if data['status'] == 'success':
                    # Display social media links found
                    social_links = data.get('social_media_links', {})
                    print(f"📱 Social media links found: {len(social_links)}")
                    for platform, url in social_links.items():
                        print(f"   • {platform}: {url}")
                    
                    # Display posts collected
                    posts = data.get('posts', {})
                    print(f"📊 Posts collected from {len(posts)} platforms:")
                    for platform, platform_data in posts.items():
                        status = platform_data.get('status', 'unknown')
                        count = platform_data.get('posts_count', 0)
                        print(f"   • {platform}: {count} posts ({status})")
                    
                    # Display Instagram comments
                    instagram_comments = data.get('instagram_comments', {})
                    total_comments = sum(
                        post_data.get('comment_count', 0) 
                        for post_data in instagram_comments.values()
                    )
                    print(f"💬 Instagram comments: {total_comments} total from {len(instagram_comments)} posts")
                    
                    # Display summary
                    summary = data.get('summary', {})
                    print(f"📋 Summary:")
                    print(f"   • Platforms analyzed: {summary.get('platforms_analyzed', 0)}")
                    print(f"   • Platforms with posts: {summary.get('platforms_with_posts', 0)}")
                    print(f"   • Total posts collected: {summary.get('total_posts_collected', 0)}")
                    print(f"   • Total Instagram comments: {summary.get('total_instagram_comments', 0)}")
                    
                    # Display Instagram analysis
                    instagram_analysis = data.get('instagram_analysis', {})
                    if instagram_analysis:
                        print(f"📈 Instagram Analysis:")
                        print(f"   • Posts analyzed: {instagram_analysis.get('total_posts_analyzed', 0)}")
                        print(f"   • Comments collected: {instagram_analysis.get('total_comments_collected', 0)}")
                        print(f"   • Success rate: {instagram_analysis.get('success_rate', 0):.1%}")
                        print(f"   • Avg comments per post: {instagram_analysis.get('average_comments_per_post', 0):.1f}")
                    
                    # Show sample comments
                    if instagram_comments:
                        print(f"💬 Sample Instagram comments:")
                        for post_url, post_data in list(instagram_comments.items())[:2]:  # Show first 2 posts
                            comments = post_data.get('comments', [])
                            print(f"   Post: {post_url}")
                            for comment in comments[:2]:  # Show first 2 comments
                                username = comment.get('username', 'unknown')
                                text = comment.get('text', '')[:50]
                                likes = comment.get('likes_count', 0)
                                print(f"     @{username}: {text}... (❤️ {likes})")
                
                return data['status'] == 'success'
            else:
                print(f"❌ Unified analysis failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Unified analysis failed: {str(e)}")
            return False
    
    def test_error_handling(self) -> bool:
        """Test error handling with invalid website URL"""
        print(f"\n🧪 Testing error handling with invalid URL...")
        
        payload = {
            "website_url": "https://this-website-does-not-exist-12345.com",
            "post_count": 2,
            "comment_limit": 3,
            "save_to_db": False
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/social-posts/competitor/analyze",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'error':
                    print("✅ Error handling works correctly")
                    print(f"   Error message: {data.get('message', 'No message')}")
                    return True
                else:
                    print("❌ Expected error but got success")
                    return False
            else:
                print(f"✅ API returned error status as expected: {response.status_code}")
                return True
                
        except Exception as e:
            print(f"❌ Error handling test failed: {str(e)}")
            return False
    
    def test_with_database_save(self) -> bool:
        """Test saving results to database"""
        print(f"\n🧪 Testing database save functionality...")
        
        payload = {
            "website_url": "https://johndeere.com",
            "post_count": 2,
            "comment_limit": 3,
            "save_to_db": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/social-posts/competitor/analyze",
                json=payload,
                timeout=180
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'success':
                    summary = data.get('summary', {})
                    competitor_id = summary.get('competitor_id')
                    
                    if competitor_id:
                        print(f"✅ Database save successful")
                        print(f"   Competitor ID: {competitor_id}")
                        return True
                    else:
                        print("⚠️  Analysis successful but no competitor ID returned")
                        return True  # Still consider it a success
                else:
                    print(f"❌ Database save failed: {data.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ Database save test failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Database save test failed: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results"""
        print("🚀 Starting Unified Competitor Analysis Tests")
        print("=" * 60)
        
        tests = {
            "health_check": self.test_health_check,
            "api_documentation": self.test_api_documentation,
            "unified_analysis_johndeere": lambda: self.test_unified_competitor_analysis("https://johndeere.com"),
            "error_handling": self.test_error_handling,
            "database_save": self.test_with_database_save
        }
        
        results = {}
        
        for test_name, test_func in tests.items():
            try:
                print(f"\n{'='*20} {test_name.upper()} {'='*20}")
                results[test_name] = test_func()
                time.sleep(2)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test {test_name} crashed: {str(e)}")
                results[test_name] = False
        
        # Print summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "✅ PASS" if passed_test else "❌ FAIL"
            print(f"{status} - {test_name}")
            if passed_test:
                passed += 1
        
        print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Unified competitor analysis is working perfectly!")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")
        
        return results


def main():
    """Main function to run the tests"""
    print("🚀 Unified Competitor Analysis Test Suite")
    print("This script tests the new unified endpoint that analyzes competitors comprehensively")
    print()
    
    # Check if API is running
    tester = UnifiedCompetitorTester()
    
    if not tester.test_health_check():
        print("\n❌ API is not running. Please start the API server first:")
        print("   cd lead_processing_api")
        print("   uvicorn app.main:app --reload")
        return
    
    # Run all tests
    results = tester.run_all_tests()
    
    # Provide next steps
    print("\n📚 NEXT STEPS:")
    if all(results.values()):
        print("✅ Unified competitor analysis is working perfectly!")
        print("   • Use the endpoint: POST /social-posts/competitor/analyze")
        print("   • Input: Just a competitor website URL")
        print("   • Output: Posts from all platforms + Instagram comments")
        print("   • Check Swagger UI: http://localhost:8000/docs")
    else:
        print("⚠️  Fix the failing tests:")
        for test_name, passed in results.items():
            if not passed:
                print(f"   • Fix: {test_name}")
        print("   • Check API logs for detailed error messages")
        print("   • Verify all dependencies are installed")
    
    print("\n🎯 USAGE EXAMPLE:")
    print("curl -X POST 'http://localhost:8000/social-posts/competitor/analyze' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{")
    print('    "website_url": "https://johndeere.com",')
    print('    "post_count": 10,')
    print('    "comment_limit": 15')
    print("  }'")


if __name__ == "__main__":
    main()
